# Kochi Boutique - Setup Instructions

## 🚀 Quick Start Guide

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- Git

### Installation Steps

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Setup Environment Variables**
   - Copy `.env` file and update values:
   ```
   PORT=3002
   MONGODB_URI=mongodb://localhost:27017/kochi-boutique
   JWT_SECRET=your-secret-key-here
   PORTER_API_KEY=your-porter-api-key
   PORTER_BASE_URL=https://api.porter.in
   ```

3. **Start MongoDB**
   - If using local MongoDB:
   ```bash
   mongod
   ```
   - Or use MongoDB Atlas cloud service

4. **Seed the Database**
   ```bash
   npm run seed
   ```

5. **Start the Application**
   ```bash
   npm start
   ```

6. **Access the Application**
   - Customer Website: http://localhost:3002
   - Manager Dashboard: http://localhost:3002/manager

## 🎯 Features Implemented

### Customer Features
- ✅ **Mobile-First Responsive Design**
- ✅ **OTP-Based Authentication** (Demo mode with console OTP)
- ✅ **Enhanced Product Filtering & Search**
- ✅ **Smart Product Cards** with ratings, wishlist, quick view
- ✅ **Advanced Filtering System** (price, category, delivery, sorting)
- ✅ **Wishlist Functionality**
- ✅ **Porter Delivery Integration** (Mock implementation)
- ✅ **WhatsApp Integration** for quick orders
- ✅ **Real-time Notifications**
- ✅ **User Profile Management**

### Manager Features
- ✅ **Complete Dashboard** with analytics
- ✅ **Dress Management** (Add, Edit, Delete)
- ✅ **Order Management** with status updates
- ✅ **Real-time Statistics**
- ✅ **File Upload** for dress images

### Technical Features
- ✅ **Express.js Backend** with RESTful APIs
- ✅ **MongoDB Database** with Mongoose ODM
- ✅ **Porter API Integration** for delivery
- ✅ **File Upload** with Multer
- ✅ **Responsive CSS** with modern styling
- ✅ **Interactive JavaScript** with enhanced UX

## 📱 Mobile-Friendly Features

### Enhanced Mobile Navigation
- Slide-out mobile menu
- Touch-friendly buttons
- Optimized for small screens
- Gesture-friendly interactions

### OTP Authentication System
- Phone number verification
- 6-digit OTP input with auto-advance
- Countdown timer with resend functionality
- Paste support for OTP codes
- Demo mode shows OTP in console

### Product Browsing Experience
- Grid and list view options
- Advanced filtering system
- Price range slider
- Sort by multiple criteria
- Load more pagination
- Quick view modals
- Wishlist functionality

### User Experience Enhancements
- Real-time notifications
- Loading states and animations
- Error handling with user feedback
- Smooth scrolling navigation
- Touch-optimized interactions

## 🛍️ Sample Data Included

The seed script creates:
- **12 Dresses** across 4 categories (Sarees, Kurtis, Dresses, Blouses)
- **5 Users** (4 customers + 1 manager)
- **5 Sample Orders** with different statuses
- **Realistic pricing** from ₹600 to ₹3000
- **Multiple delivery options** and customizations

## 🔧 Porter Integration

### Current Implementation
- Mock Porter API responses for development
- Real-time fare quotes
- Order tracking simulation
- Delivery status updates
- Integration ready for production

### To Enable Real Porter Integration
1. Get Porter API credentials from https://porter.in/
2. Update `PORTER_API_KEY` in `.env`
3. Update `PORTER_BASE_URL` to production URL
4. The system will automatically switch from mock to real API

## 📊 Database Schema

### Dresses Collection
```javascript
{
  name: String,
  category: String,
  basePrice: Number,
  image: String,
  features: [String],
  description: String,
  isActive: Boolean,
  createdAt: Date
}
```

### Orders Collection
```javascript
{
  orderId: String (unique),
  customerName: String,
  customerPhone: String,
  dressId: ObjectId (ref: Dress),
  measurements: { bust, waist, hip, length },
  customizations: { fabric, color, specialInstructions },
  deliveryOption: String,
  deliveryAddress: String,
  totalAmount: Number,
  status: String,
  porterOrderId: String,
  createdAt: Date
}
```

### Users Collection
```javascript
{
  name: String,
  email: String (unique),
  phone: String,
  role: String (customer/manager),
  createdAt: Date
}
```

## 🎨 UI/UX Highlights

### Color Scheme
- Primary: Pink (#ff6b9d)
- Secondary: Dark Pink (#c44569)
- Accent: Gold (#ffd700)
- Background: Light Gray (#f8f9fa)

### Typography
- Headers: Playfair Display (elegant serif)
- Body: Poppins (modern sans-serif)
- Icons: Font Awesome 6

### Responsive Breakpoints
- Mobile: < 480px
- Tablet: 481px - 768px
- Desktop: > 768px

## 🚀 Production Deployment

### Environment Setup
1. Set `NODE_ENV=production`
2. Configure production MongoDB URI
3. Set up real Porter API credentials
4. Enable HTTPS
5. Set up proper domain

### Security Considerations
- Implement rate limiting
- Add input validation
- Set up CORS properly
- Use secure headers
- Implement proper authentication

### Performance Optimizations
- Enable gzip compression
- Optimize images
- Implement caching
- Use CDN for static assets
- Database indexing

## 📞 Support & Contact

For technical support or customization requests:
- Email: <EMAIL>
- Phone: +91 9876543210
- Address: MG Road, Kochi, Kerala 682035

## 🔄 Development Workflow

### Adding New Features
1. Update database schema if needed
2. Create API endpoints in `server.js`
3. Update frontend in `index.html` and `script.js`
4. Test on mobile and desktop
5. Update documentation

### Testing
- Test OTP flow (check console for demo OTP)
- Test all responsive breakpoints
- Verify Porter integration (mock mode)
- Test manager dashboard functionality
- Verify database operations

## 📝 Notes

- OTP system is in demo mode - check browser console for OTP codes
- Porter integration uses mock responses for development
- All images are from Unsplash for demo purposes
- WhatsApp integration uses demo phone number
- Database seeding creates realistic sample data

## 🎉 Ready to Launch!

Your Kochi Boutique application is now ready with:
- Full mobile responsiveness
- OTP authentication
- Enhanced product browsing
- Manager dashboard
- Porter delivery integration
- Professional UI/UX design

Start the application and explore all the features!