<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manager Dashboard - <PERSON><PERSON></title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard {
            padding: 100px 20px 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .dashboard-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .dashboard-header h1 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card i {
            font-size: 2.5rem;
            color: #ff6b9d;
            margin-bottom: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-weight: 500;
        }
        
        .dashboard-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .tab-btn {
            background: white;
            border: 2px solid #ff6b9d;
            color: #ff6b9d;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .tab-btn.active,
        .tab-btn:hover {
            background: #ff6b9d;
            color: white;
        }
        
        .tab-content {
            display: none;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .tab-content.active {
            display: block;
        }
        
        .add-dress-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .add-dress-form input,
        .add-dress-form select,
        .add-dress-form textarea {
            padding: 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .add-dress-form textarea {
            grid-column: 1 / -1;
            resize: vertical;
        }
        
        .add-dress-form input:focus,
        .add-dress-form select:focus,
        .add-dress-form textarea:focus {
            outline: none;
            border-color: #ff6b9d;
        }
        
        .file-upload {
            grid-column: 1 / -1;
            border: 2px dashed #ff6b9d;
            padding: 2rem;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-upload:hover {
            background: rgba(255,107,157,0.1);
        }
        
        .file-upload input {
            display: none;
        }
        
        .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .orders-table th,
        .orders-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .orders-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-confirmed { background: #d4edda; color: #155724; }
        .status-in-progress { background: #cce5ff; color: #004085; }
        .status-ready { background: #e2e3e5; color: #383d41; }
        .status-dispatched { background: #f8d7da; color: #721c24; }
        .status-delivered { background: #d1ecf1; color: #0c5460; }
        
        .action-btn {
            background: #ff6b9d;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            margin: 0.2rem;
        }
        
        .action-btn:hover {
            background: #c44569;
        }
        
        .dress-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        
        .dress-item {
            border: 1px solid #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .dress-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .dress-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .dress-item-info {
            padding: 1rem;
        }
        
        .dress-item-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        @media (max-width: 768px) {
            .add-dress-form {
                grid-template-columns: 1fr;
            }
            
            .orders-table {
                font-size: 0.8rem;
            }
            
            .orders-table th,
            .orders-table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-cut"></i> Kochi Boutique</h1>
                <p>Manager Dashboard</p>
            </div>
            <nav class="nav-menu">
                <a href="/" class="nav-link">Back to Website</a>
                <button class="btn-login" onclick="logout()">Logout</button>
            </nav>
        </div>
    </header>

    <!-- Dashboard -->
    <div class="dashboard">
        <div class="container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <h1>Welcome, Manager!</h1>
                <p>Manage your boutique operations from here</p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-shopping-cart"></i>
                    <div class="stat-number" id="totalOrders">0</div>
                    <div class="stat-label">Total Orders</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-clock"></i>
                    <div class="stat-number" id="pendingOrders">0</div>
                    <div class="stat-label">Pending Orders</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-check-circle"></i>
                    <div class="stat-number" id="completedOrders">0</div>
                    <div class="stat-label">Completed Orders</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-rupee-sign"></i>
                    <div class="stat-number" id="totalRevenue">₹0</div>
                    <div class="stat-label">Total Revenue</div>
                </div>
            </div>

            <!-- Dashboard Tabs -->
            <div class="dashboard-tabs">
                <button class="tab-btn active" onclick="showTab('orders')">Orders</button>
                <button class="tab-btn" onclick="showTab('dresses')">Manage Dresses</button>
                <button class="tab-btn" onclick="showTab('add-dress')">Add New Dress</button>
                <button class="tab-btn" onclick="showTab('analytics')">Analytics</button>
            </div>

            <!-- Orders Tab -->
            <div id="orders" class="tab-content active">
                <h2>Recent Orders</h2>
                <div style="overflow-x: auto;">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Dress</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Delivery</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="ordersTableBody">
                            <!-- Orders will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Manage Dresses Tab -->
            <div id="dresses" class="tab-content">
                <h2>Manage Dresses</h2>
                <div class="dress-grid" id="dressGrid">
                    <!-- Dresses will be loaded here -->
                </div>
            </div>

            <!-- Add New Dress Tab -->
            <div id="add-dress" class="tab-content">
                <h2>Add New Dress</h2>
                <form class="add-dress-form" id="addDressForm">
                    <input type="text" name="name" placeholder="Dress Name" required>
                    <select name="category" required>
                        <option value="">Select Category</option>
                        <option value="saree">Saree</option>
                        <option value="kurti">Kurti</option>
                        <option value="dress">Western Dress</option>
                        <option value="blouse">Blouse</option>
                    </select>
                    <input type="number" name="basePrice" placeholder="Base Price (₹)" required>
                    <input type="text" name="features" placeholder="Features (comma separated)">
                    <textarea name="description" placeholder="Description" rows="3"></textarea>
                    <div class="file-upload" onclick="document.getElementById('imageUpload').click()">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: #ff6b9d; margin-bottom: 1rem;"></i>
                        <p>Click to upload dress image</p>
                        <input type="file" id="imageUpload" name="image" accept="image/*" required>
                    </div>
                    <button type="submit" class="cta-button" style="grid-column: 1 / -1;">
                        <i class="fas fa-plus"></i> Add Dress
                    </button>
                </form>
            </div>

            <!-- Analytics Tab -->
            <div id="analytics" class="tab-content">
                <h2>Analytics & Reports</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <i class="fas fa-chart-line"></i>
                        <div class="stat-number">85%</div>
                        <div class="stat-label">Customer Satisfaction</div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-bolt"></i>
                        <div class="stat-number">6.5hrs</div>
                        <div class="stat-label">Avg. Express Delivery</div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-redo"></i>
                        <div class="stat-number">12%</div>
                        <div class="stat-label">Return Rate</div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-star"></i>
                        <div class="stat-number">4.8</div>
                        <div class="stat-label">Average Rating</div>
                    </div>
                </div>
                
                <div style="background: white; padding: 2rem; border-radius: 15px; margin-top: 2rem;">
                    <h3>Popular Categories</h3>
                    <div style="display: flex; gap: 1rem; margin-top: 1rem; flex-wrap: wrap;">
                        <div style="flex: 1; min-width: 200px;">
                            <div style="background: #ff6b9d; height: 20px; border-radius: 10px; margin-bottom: 0.5rem;"></div>
                            <p>Kurtis - 35%</p>
                        </div>
                        <div style="flex: 1; min-width: 200px;">
                            <div style="background: #c44569; height: 15px; border-radius: 10px; margin-bottom: 0.5rem;"></div>
                            <p>Blouses - 28%</p>
                        </div>
                        <div style="flex: 1; min-width: 200px;">
                            <div style="background: #ff9a9e; height: 12px; border-radius: 10px; margin-bottom: 0.5rem;"></div>
                            <p>Sarees - 22%</p>
                        </div>
                        <div style="flex: 1; min-width: 200px;">
                            <div style="background: #fecfef; height: 8px; border-radius: 10px; margin-bottom: 0.5rem;"></div>
                            <p>Western - 15%</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dashboard functionality
        let currentOrders = [];
        let currentDresses = [];

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            loadOrders();
            loadDresses();
        });

        // Load dashboard analytics
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/analytics');
                const data = await response.json();
                
                document.getElementById('totalOrders').textContent = data.totalOrders;
                document.getElementById('pendingOrders').textContent = data.pendingOrders;
                document.getElementById('completedOrders').textContent = data.completedOrders;
                document.getElementById('totalRevenue').textContent = `₹${data.totalRevenue.toLocaleString()}`;
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Load orders
        async function loadOrders() {
            try {
                const response = await fetch('/api/orders');
                const orders = await response.json();
                currentOrders = orders;
                displayOrders(orders);
            } catch (error) {
                console.error('Error loading orders:', error);
            }
        }

        // Display orders in table
        function displayOrders(orders) {
            const tbody = document.getElementById('ordersTableBody');
            tbody.innerHTML = '';

            orders.forEach(order => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${order.orderId}</td>
                    <td>
                        <div>${order.customerName || 'N/A'}</div>
                        <small>${order.customerPhone || ''}</small>
                    </td>
                    <td>${order.dressId?.name || 'N/A'}</td>
                    <td>₹${order.totalAmount?.toLocaleString() || '0'}</td>
                    <td><span class="status-badge status-${order.status}">${order.status}</span></td>
                    <td>${order.deliveryOption || 'N/A'}</td>
                    <td>
                        <select onchange="updateOrderStatus('${order._id}', this.value)" class="action-btn">
                            <option value="${order.status}">${order.status}</option>
                            <option value="pending">Pending</option>
                            <option value="confirmed">Confirmed</option>
                            <option value="in-progress">In Progress</option>
                            <option value="ready">Ready</option>
                            <option value="dispatched">Dispatched</option>
                            <option value="delivered">Delivered</option>
                        </select>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Update order status
        async function updateOrderStatus(orderId, newStatus) {
            try {
                const response = await fetch(`/api/orders/${orderId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ status: newStatus })
                });

                if (response.ok) {
                    loadOrders(); // Reload orders
                    loadDashboardData(); // Reload stats
                    alert('Order status updated successfully!');
                }
            } catch (error) {
                console.error('Error updating order status:', error);
                alert('Error updating order status');
            }
        }

        // Load dresses
        async function loadDresses() {
            try {
                const response = await fetch('/api/dresses');
                const dresses = await response.json();
                currentDresses = dresses;
                displayDresses(dresses);
            } catch (error) {
                console.error('Error loading dresses:', error);
            }
        }

        // Display dresses
        function displayDresses(dresses) {
            const grid = document.getElementById('dressGrid');
            grid.innerHTML = '';

            dresses.forEach(dress => {
                const dressItem = document.createElement('div');
                dressItem.className = 'dress-item';
                dressItem.innerHTML = `
                    <img src="${dress.image}" alt="${dress.name}" onerror="this.src='https://via.placeholder.com/300x200?text=No+Image'">
                    <div class="dress-item-info">
                        <h3>${dress.name}</h3>
                        <p><strong>Category:</strong> ${dress.category}</p>
                        <p><strong>Price:</strong> ₹${dress.basePrice}</p>
                        <p><strong>Features:</strong> ${dress.features?.join(', ') || 'None'}</p>
                        <div class="dress-item-actions">
                            <button class="action-btn" onclick="editDress('${dress._id}')">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="action-btn" onclick="deleteDress('${dress._id}')" style="background: #e74c3c;">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                `;
                grid.appendChild(dressItem);
            });
        }

        // Add new dress
        document.getElementById('addDressForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            try {
                const response = await fetch('/api/dresses', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    alert('Dress added successfully!');
                    this.reset();
                    loadDresses();
                } else {
                    alert('Error adding dress');
                }
            } catch (error) {
                console.error('Error adding dress:', error);
                alert('Error adding dress');
            }
        });

        // Edit dress (simplified - would open a modal in production)
        function editDress(dressId) {
            const dress = currentDresses.find(d => d._id === dressId);
            if (dress) {
                const newName = prompt('Enter new name:', dress.name);
                const newPrice = prompt('Enter new price:', dress.basePrice);
                
                if (newName && newPrice) {
                    updateDress(dressId, { name: newName, basePrice: parseFloat(newPrice) });
                }
            }
        }

        // Update dress
        async function updateDress(dressId, updateData) {
            try {
                const formData = new FormData();
                Object.keys(updateData).forEach(key => {
                    formData.append(key, updateData[key]);
                });

                const response = await fetch(`/api/dresses/${dressId}`, {
                    method: 'PUT',
                    body: formData
                });

                if (response.ok) {
                    alert('Dress updated successfully!');
                    loadDresses();
                } else {
                    alert('Error updating dress');
                }
            } catch (error) {
                console.error('Error updating dress:', error);
                alert('Error updating dress');
            }
        }

        // Delete dress
        async function deleteDress(dressId) {
            if (confirm('Are you sure you want to delete this dress?')) {
                try {
                    const response = await fetch(`/api/dresses/${dressId}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        alert('Dress deleted successfully!');
                        loadDresses();
                    } else {
                        alert('Error deleting dress');
                    }
                } catch (error) {
                    console.error('Error deleting dress:', error);
                    alert('Error deleting dress');
                }
            }
        }

        // Tab functionality
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Logout function
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = '/';
            }
        }

        // File upload preview
        document.getElementById('imageUpload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.createElement('img');
                    preview.src = e.target.result;
                    preview.style.maxWidth = '200px';
                    preview.style.marginTop = '1rem';
                    preview.style.borderRadius = '8px';
                    
                    const uploadDiv = document.querySelector('.file-upload');
                    const existingPreview = uploadDiv.querySelector('img');
                    if (existingPreview) {
                        existingPreview.remove();
                    }
                    uploadDiv.appendChild(preview);
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>