// Profile management functionality
class ProfileManager {
    constructor() {
        this.token = localStorage.getItem('token') || sessionStorage.getItem('token');
        this.user = null;
        this.addresses = [];
        this.measurements = [];
        this.orders = [];
        
        this.init();
    }

    init() {
        if (!this.token) {
            console.log('No token found, redirecting to login');
            window.location.href = 'login.html?redirect=profile.html';
            return;
        }

        console.log('Profile manager initialized with token');
        this.loadUserData();
        this.setupEventListeners();
    }

    async loadUserData() {
        try {
            // Check if using demo token - load demo data immediately
            if (this.token && this.token.startsWith('demo-token')) {
                // Load demo user data
                this.user = {
                    id: 1,
                    fullName: 'Demo User',
                    email: '<EMAIL>',
                    phone: '+91 98765 43210',
                    dateJoined: '2024-01-15',
                    totalOrders: 3,
                    loyaltyPoints: 150
                };
                this.updateUI();
                return;
            }

            // Only make API call for non-demo tokens
            const response = await fetch('/api/auth/profile', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.user = data.user;
                this.updateUI();
            } else if (response.status === 401) {
                this.handleUnauthorized();
            } else {
                throw new Error('Failed to load profile');
            }
        } catch (error) {
            console.error('Error loading user data:', error);
            this.showNotification('Failed to load profile data', 'error');
        }
    }

    updateUI() {
        if (!this.user) return;

        // Update header and sidebar with safe property access
        const userName = document.getElementById('userName');
        const sidebarName = document.getElementById('sidebarName');
        const sidebarEmail = document.getElementById('sidebarEmail');

        if (userName && this.user.fullName) {
            userName.textContent = this.user.fullName;
        }
        if (sidebarName && this.user.fullName) {
            sidebarName.textContent = this.user.fullName;
        }
        if (sidebarEmail && this.user.email) {
            sidebarEmail.textContent = this.user.email;
        }

        // Update avatar initial safely
        const avatarInitial = document.getElementById('avatarInitial');
        if (avatarInitial && this.user.fullName) {
            avatarInitial.textContent = this.user.fullName.charAt(0).toUpperCase();
        }

        // Update profile form safely
        const profileFullName = document.getElementById('profileFullName');
        const profileEmail = document.getElementById('profileEmail');
        const profilePhone = document.getElementById('profilePhone');
        const profileDOB = document.getElementById('profileDOB');
        const profileSize = document.getElementById('profileSize');

        if (profileFullName) profileFullName.value = this.user.fullName || '';
        if (profileEmail) profileEmail.value = this.user.email || '';
        if (profilePhone) profilePhone.value = this.user.phone || this.user.phoneNumber || '';
        if (profileDOB) profileDOB.value = this.user.dateOfBirth ? this.user.dateOfBirth.split('T')[0] : '';
        if (profileSize) profileSize.value = this.user.preferredSizeType || '';

        // Update hidden username field for password form accessibility
        const hiddenUsername = document.querySelector('input[name="username"]');
        if (hiddenUsername && this.user.email) {
            hiddenUsername.value = this.user.email;
        }
    }

    setupEventListeners() {
        // Profile form
        document.getElementById('profileForm').addEventListener('submit', (e) => this.updateProfile(e));
        
        // Address form
        document.getElementById('addressForm').addEventListener('submit', (e) => this.addAddress(e));
        
        // Measurement form
        document.getElementById('measurementForm').addEventListener('submit', (e) => this.addMeasurement(e));
        
        // Change password form
        document.getElementById('changePasswordForm').addEventListener('submit', (e) => this.changePassword(e));
    }

    async updateProfile(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);

        try {
            const response = await fetch('/api/auth/profile', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                const result = await response.json();
                this.user = result.user;
                this.updateUI();
                this.showNotification('Profile updated successfully', 'success');
            } else {
                const error = await response.json();
                this.showNotification(error.error || 'Failed to update profile', 'error');
            }
        } catch (error) {
            console.error('Error updating profile:', error);
            this.showNotification('Failed to update profile', 'error');
        }
    }

    async loadAddresses() {
        try {
            // Check if using demo token - load demo data immediately
            if (this.token && this.token.startsWith('demo-token')) {
                // Load demo addresses
                this.addresses = [
                    {
                        id: 1,
                        name: 'Home',
                        fullName: 'Demo User',
                        phone: '+91 98765 43210',
                        email: '<EMAIL>',
                        address: '123 Fashion Street, Edapally',
                        city: 'Kochi',
                        state: 'Kerala',
                        zipCode: '682024',
                        country: 'India',
                        isDefault: true
                    },
                    {
                        id: 2,
                        name: 'Office',
                        fullName: 'Demo User',
                        phone: '+91 98765 43210',
                        email: '<EMAIL>',
                        address: '456 Business Park, Kakkanad',
                        city: 'Kochi',
                        state: 'Kerala',
                        zipCode: '682030',
                        country: 'India',
                        isDefault: false
                    }
                ];
                this.renderAddresses();
                return;
            }

            // Only make API call for non-demo tokens
            const response = await fetch('/api/auth/addresses', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.addresses = data.addresses;
                this.renderAddresses();
            } else {
                throw new Error('API call failed');
            }
        } catch (error) {
            console.error('Error loading addresses:', error);
            this.showNotification('Failed to load addresses', 'error');
        }
    }

    renderAddresses() {
        const container = document.getElementById('addressesList');
        
        if (this.addresses.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-map-marker-alt"></i>
                    <h3>No addresses saved</h3>
                    <p>Add your first address to make ordering faster</p>
                    <button class="btn btn-primary" onclick="showAddAddressModal()">Add Address</button>
                </div>
            `;
            return;
        }

        container.innerHTML = this.addresses.map(address => `
            <div class="address-card">
                <div class="card-header">
                    <div class="card-title">
                        ${address.label}
                        ${address.isDefault ? '<span class="default-badge">Default</span>' : ''}
                    </div>
                    <div class="card-actions">
                        <button onclick="profileManager.editAddress('${address._id}')" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="profileManager.deleteAddress('${address._id}')" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    ${address.streetAddress}<br>
                    ${address.city}, ${address.state} ${address.zipCode}
                </div>
            </div>
        `).join('');
    }

    async addAddress(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);
        data.isDefault = document.getElementById('isDefaultAddress').checked;

        try {
            const response = await fetch('/api/auth/addresses', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                this.showNotification('Address added successfully', 'success');
                closeAddressModal();
                this.loadAddresses();
            } else {
                const error = await response.json();
                this.showNotification(error.error || 'Failed to add address', 'error');
            }
        } catch (error) {
            console.error('Error adding address:', error);
            this.showNotification('Failed to add address', 'error');
        }
    }

    async editAddress(addressId) {
        // Implementation for editing address
        this.showNotification('Edit functionality coming soon', 'info');
    }

    async deleteAddress(addressId) {
        if (!confirm('Are you sure you want to delete this address?')) return;

        try {
            const response = await fetch(`/api/auth/addresses/${addressId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                this.showNotification('Address deleted successfully', 'success');
                this.loadAddresses();
            } else {
                this.showNotification('Failed to delete address', 'error');
            }
        } catch (error) {
            console.error('Error deleting address:', error);
            this.showNotification('Failed to delete address', 'error');
        }
    }

    async loadMeasurements() {
        try {
            // Check if using demo token - load demo data immediately
            if (this.token && this.token.startsWith('demo-token')) {
                // Load demo measurements
                this.measurements = [
                    {
                        id: 1,
                        name: 'Standard Fit',
                        bust: 36,
                        waist: 28,
                        hips: 38,
                        length: 42,
                        shoulder: 15,
                        sleeve: 24,
                        createdAt: '2024-01-15',
                        isDefault: true
                    },
                    {
                        id: 2,
                        name: 'Party Dress Fit',
                        bust: 35,
                        waist: 27,
                        hips: 37,
                        length: 38,
                        shoulder: 15,
                        sleeve: 22,
                        createdAt: '2024-02-10',
                        isDefault: false
                    }
                ];
                this.renderMeasurements();
                return;
            }

            // Only make API call for non-demo tokens
            const response = await fetch('/api/auth/measurements', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.measurements = data.measurements;
                this.renderMeasurements();
            } else {
                throw new Error('API call failed');
            }
        } catch (error) {
            console.error('Error loading measurements:', error);
            this.showNotification('Failed to load measurements', 'error');
        }
    }

    renderMeasurements() {
        const container = document.getElementById('measurementsList');
        
        if (this.measurements.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-ruler"></i>
                    <h3>No measurements saved</h3>
                    <p>Add your measurements for perfect fitting clothes</p>
                    <button class="btn btn-primary" onclick="showAddMeasurementModal()">Add Measurement</button>
                </div>
            `;
            return;
        }

        container.innerHTML = this.measurements.map(measurement => `
            <div class="measurement-card">
                <div class="card-header">
                    <div class="card-title">
                        ${measurement.label}
                        ${measurement.isDefault ? '<span class="default-badge">Default</span>' : ''}
                    </div>
                    <div class="card-actions">
                        <button onclick="profileManager.editMeasurement('${measurement._id}')" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="profileManager.deleteMeasurement('${measurement._id}')" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <strong>Measurements:</strong><br>
                    Bust: ${measurement.bust}${measurement.unit}<br>
                    Waist: ${measurement.waist}${measurement.unit}<br>
                    Hip: ${measurement.hip}${measurement.unit}<br>
                    Length: ${measurement.length}${measurement.unit}
                </div>
            </div>
        `).join('');
    }

    async addMeasurement(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);
        data.isDefault = document.getElementById('isDefaultMeasurement').checked;

        try {
            const response = await fetch('/api/auth/measurements', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                this.showNotification('Measurement added successfully', 'success');
                closeMeasurementModal();
                this.loadMeasurements();
            } else {
                const error = await response.json();
                this.showNotification(error.error || 'Failed to add measurement', 'error');
            }
        } catch (error) {
            console.error('Error adding measurement:', error);
            this.showNotification('Failed to add measurement', 'error');
        }
    }

    async editMeasurement(measurementId) {
        // Implementation for editing measurement
        this.showNotification('Edit functionality coming soon', 'info');
    }

    async deleteMeasurement(measurementId) {
        if (!confirm('Are you sure you want to delete this measurement?')) return;

        try {
            const response = await fetch(`/api/auth/measurements/${measurementId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                this.showNotification('Measurement deleted successfully', 'success');
                this.loadMeasurements();
            } else {
                this.showNotification('Failed to delete measurement', 'error');
            }
        } catch (error) {
            console.error('Error deleting measurement:', error);
            this.showNotification('Failed to delete measurement', 'error');
        }
    }

    async loadOrders() {
        try {
            // Check if using demo token - load demo data immediately
            if (this.token && this.token.startsWith('demo-token')) {
                // Load demo orders with proper structure
                this.orders = [
                    {
                        _id: 'ORD-2024001',
                        orderNumber: 'ORD-2024001',
                        status: 'Completed',
                        createdAt: '2024-01-15T10:00:00Z',
                        deliveryDate: '2024-01-25',
                        totalAmount: 8500,
                        items: [
                            {
                                name: 'Elegant Evening Gown',
                                fabric: 'Silk',
                                color: 'Navy Blue',
                                size: 'M',
                                quantity: 1,
                                price: 8500
                            }
                        ],
                        deliveryAddress: {
                            streetAddress: '123 Fashion Street, Edapally',
                            city: 'Kochi',
                            state: 'Kerala',
                            zipCode: '682024'
                        }
                    },
                    {
                        _id: 'ORD-2024002',
                        orderNumber: 'ORD-2024002',
                        status: 'In Progress',
                        createdAt: '2024-02-10T14:30:00Z',
                        estimatedDelivery: '2024-02-20',
                        totalAmount: 4500,
                        items: [
                            {
                                name: 'Casual Summer Dress',
                                fabric: 'Cotton',
                                color: 'Floral Print',
                                size: 'M',
                                quantity: 1,
                                price: 4500
                            }
                        ],
                        deliveryAddress: {
                            streetAddress: '123 Fashion Street, Edapally',
                            city: 'Kochi',
                            state: 'Kerala',
                            zipCode: '682024'
                        }
                    },
                    {
                        _id: 'ORD-2024003',
                        orderNumber: 'ORD-2024003',
                        status: 'Pending',
                        createdAt: '2024-02-15T09:15:00Z',
                        estimatedDelivery: '2024-02-25',
                        totalAmount: 6500,
                        items: [
                            {
                                name: 'Business Formal Dress',
                                fabric: 'Wool Blend',
                                color: 'Charcoal Gray',
                                size: 'M',
                                quantity: 1,
                                price: 6500
                            }
                        ],
                        deliveryAddress: {
                            streetAddress: '456 Business Park, Kakkanad',
                            city: 'Kochi',
                            state: 'Kerala',
                            zipCode: '682030'
                        }
                    }
                ];
                this.renderOrders();
                this.updateOrderStats();
                return;
            }

            // Only make API call for non-demo tokens
            const response = await fetch('/api/auth/orders', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.orders = data.orders;
                this.renderOrders();
                this.updateOrderStats();
            } else {
                throw new Error('API call failed');
            }
        } catch (error) {
            console.error('Error loading orders:', error);
            this.showNotification('Failed to load orders', 'error');
        }
    }

    renderOrders() {
        const container = document.getElementById('ordersList');
        
        if (!this.orders || this.orders.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-shopping-bag"></i>
                    <h3>No orders yet</h3>
                    <p>Start shopping to see your order history</p>
                    <button class="btn btn-primary" onclick="window.location.href='/'">Browse Dresses</button>
                </div>
            `;
            return;
        }

        container.innerHTML = this.orders.map(order => {
            // Safe property access with fallbacks
            const orderNumber = order.orderNumber || order.id || 'N/A';
            const createdAt = order.createdAt || order.orderDate || new Date().toISOString();
            const status = order.status || 'Unknown';
            const totalAmount = order.totalAmount || 0;
            const itemsCount = order.items ? order.items.length : 1;
            const streetAddress = order.deliveryAddress ? order.deliveryAddress.streetAddress : 'Address not available';
            const city = order.deliveryAddress ? order.deliveryAddress.city : '';
            const orderId = order._id || order.id || orderNumber;

            return `
                <div class="order-card">
                    <div class="order-header">
                        <div>
                            <strong>Order #${orderNumber}</strong>
                            <span class="order-date">${new Date(createdAt).toLocaleDateString()}</span>
                        </div>
                        <span class="order-status status-${status.toLowerCase().replace(/\s+/g, '-')}">${status}</span>
                    </div>
                    <div class="order-details">
                        <p><strong>Total:</strong> ₹${totalAmount.toLocaleString()}</p>
                        <p><strong>Items:</strong> ${itemsCount} item(s)</p>
                        <p><strong>Delivery Address:</strong> ${streetAddress}${city ? ', ' + city : ''}</p>
                    </div>
                    <div class="order-actions">
                        <button class="btn btn-secondary" onclick="window.profileManager.viewOrder('${orderId}')">View Details</button>
                    </div>
                </div>
            `;
        }).join('');
    }

    updateOrderStats() {
        if (!this.orders) {
            this.orders = [];
        }

        const total = this.orders.length;
        const pending = this.orders.filter(order => order.status === 'Pending').length;
        const completed = this.orders.filter(order => order.status === 'Completed').length;

        const totalElement = document.getElementById('totalOrders');
        const pendingElement = document.getElementById('pendingOrders');
        const completedElement = document.getElementById('completedOrders');

        if (totalElement) totalElement.textContent = total;
        if (pendingElement) pendingElement.textContent = pending;
        if (completedElement) completedElement.textContent = completed;
    }

    viewOrder(orderId) {
        // Find the order
        const order = this.orders.find(o => o._id === orderId || o.id === orderId);
        if (!order) {
            this.showNotification('Order not found', 'error');
            return;
        }

        // For demo purposes, show order details in an alert
        // In production, this would open a detailed order view
        const orderDetails = `
Order #${order.orderNumber || order.id}
Status: ${order.status}
Date: ${new Date(order.createdAt || order.orderDate).toLocaleDateString()}
Total: ₹${(order.totalAmount || 0).toLocaleString()}
Items: ${order.items ? order.items.length : 1} item(s)
${order.items ? order.items.map(item => `- ${item.name} (${item.fabric}, ${item.color})`).join('\n') : ''}
        `.trim();

        alert(orderDetails);

        // TODO: In production, implement proper order details modal or page
        // this.showOrderDetailsModal(order);
    }

    async changePassword(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);

        if (data.newPassword !== data.confirmNewPassword) {
            this.showNotification('New passwords do not match', 'error');
            return;
        }

        try {
            const response = await fetch('/api/auth/change-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify({
                    currentPassword: data.currentPassword,
                    newPassword: data.newPassword
                })
            });

            if (response.ok) {
                this.showNotification('Password changed successfully', 'success');
                e.target.reset();
            } else {
                const error = await response.json();
                this.showNotification(error.error || 'Failed to change password', 'error');
            }
        } catch (error) {
            console.error('Error changing password:', error);
            this.showNotification('Failed to change password', 'error');
        }
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => document.body.removeChild(notification), 300);
        }, 3000);
    }

    handleUnauthorized() {
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
        window.location.href = '/login.html';
    }

    resetProfileForm() {
        this.updateUI();
    }
}

// Initialize profile manager and make it globally available
window.profileManager = new ProfileManager();

// Global functions for HTML onclick handlers
function showSection(sectionName) {
    window.profileManager.showSection(sectionName);
}

function showAddAddressModal() {
    document.getElementById('addressModal').classList.add('active');
}

function closeAddressModal() {
    document.getElementById('addressModal').classList.remove('active');
    document.getElementById('addressForm').reset();
}

function showAddMeasurementModal() {
    document.getElementById('measurementModal').classList.add('active');
}

function closeMeasurementModal() {
    document.getElementById('measurementModal').classList.remove('active');
    document.getElementById('measurementForm').reset();
}

function logout() {
    window.profileManager.handleUnauthorized();
}

// Close modals when clicking outside
window.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.classList.remove('active');
    }
});