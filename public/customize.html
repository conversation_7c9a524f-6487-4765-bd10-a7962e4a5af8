<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customize Your Dress - <PERSON><PERSON>'s Couture</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><PERSON><PERSON>'s Couture</h1>
                <p>Premium Custom Tailoring</p>
            </div>
            <nav class="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="index.html#dresses" class="nav-link">Dresses</a>
                <a href="profile.html" class="nav-link">Profile</a>
                <button id="logout-btn" class="btn-login">Logout</button>
            </nav>
            <div class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation -->
    <div class="mobile-nav" id="mobile-nav">
        <div class="mobile-nav-header">
            <h3>Nishi's Couture</h3>
            <span class="mobile-nav-close" onclick="toggleMobileMenu()">&times;</span>
        </div>
        <div class="mobile-nav-links">
            <a href="index.html">Home</a>
            <a href="index.html#dresses">Dresses</a>
            <a href="profile.html">Profile</a>
            <a href="#" onclick="logout()">Logout</a>
        </div>
    </div>
    <div class="mobile-overlay" id="mobile-overlay" onclick="closeMobileMenu()"></div>

    <!-- Main Content -->
    <main class="main-content" style="padding-top: 100px; min-height: 100vh;">
        <div class="container">
            <div class="customization-container">
                <!-- Progress Header -->
                <header class="customization-header">
                    <h1>Customize Your Perfect Dress</h1>
                    <p class="customization-subtitle">Follow our guided process to create a dress that's uniquely yours</p>

                    <!-- Progress Indicator -->
                    <div class="progress-indicator" role="progressbar" aria-valuenow="1" aria-valuemin="1" aria-valuemax="6">
                        <div class="progress-steps">
                            <div class="progress-step active" data-step="1">
                                <div class="step-number">1</div>
                                <div class="step-label">Preview</div>
                            </div>
                            <div class="progress-step" data-step="2">
                                <div class="step-number">2</div>
                                <div class="step-label">Fabric</div>
                            </div>
                            <div class="progress-step" data-step="3">
                                <div class="step-number">3</div>
                                <div class="step-label">Color</div>
                            </div>
                            <div class="progress-step" data-step="4">
                                <div class="step-number">4</div>
                                <div class="step-label">Size</div>
                            </div>
                            <div class="progress-step" data-step="5">
                                <div class="step-number">5</div>
                                <div class="step-label">Measurements</div>
                            </div>
                            <div class="progress-step" data-step="6">
                                <div class="step-number">6</div>
                                <div class="step-label">Delivery</div>
                            </div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 16.67%"></div>
                        </div>
                    </div>
                </header>

                <!-- Dress Preview -->
                <div class="customization-preview">
                    <div class="dress-preview">
                        <div class="dress-image-container">
                            <img id="dress-image" src="" alt="Selected Dress" loading="eager">
                            <div class="image-badges">
                                <span class="badge premium">Premium</span>
                                <span class="badge custom">Custom Fit</span>
                            </div>
                        </div>
                        <div class="dress-info">
                            <h2 id="dress-name"></h2>
                            <p id="dress-description"></p>
                            <div class="price-display">
                                <span class="price-label">Starting from:</span>
                                <span class="price-value">₹<span id="dress-price">0</span></span>
                                <span class="price-note">*Final price may vary based on customizations</span>
                            </div>
                            <div class="dress-features">
                                <div class="feature-tag">
                                    <i class="fas fa-star" aria-hidden="true"></i>
                                    <span>Premium Quality</span>
                                </div>
                                <div class="feature-tag">
                                    <i class="fas fa-ruler" aria-hidden="true"></i>
                                    <span>Custom Fit</span>
                                </div>
                                <div class="feature-tag">
                                    <i class="fas fa-shipping-fast" aria-hidden="true"></i>
                                    <span>Fast Delivery</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customization Form -->
                <form id="customization-form" class="customization-form">
                    <!-- Step 1: Fabric Selection -->
                    <div class="form-section active" id="step-fabric" data-step="2">
                        <div class="section-header">
                            <h3>
                                <i class="fas fa-cut" aria-hidden="true"></i>
                                Choose Your Fabric
                            </h3>
                            <p class="section-description">Select from our premium fabric collection. Each fabric offers unique texture, drape, and comfort.</p>
                        </div>

                        <div class="fabric-options" id="fabric-options" role="radiogroup" aria-label="Fabric selection">
                            <!-- Fabric options will be populated dynamically -->
                        </div>

                        <div class="fabric-info">
                            <div class="info-card" id="fabric-details">
                                <h4>Fabric Details</h4>
                                <p>Select a fabric to see detailed information about its properties and care instructions.</p>
                            </div>
                        </div>

                        <div class="step-navigation">
                            <button type="button" class="btn-secondary" onclick="previousStep()" disabled>
                                <i class="fas fa-arrow-left" aria-hidden="true"></i>
                                Previous
                            </button>
                            <button type="button" class="btn-primary" onclick="nextStep()" disabled id="fabric-next">
                                Next: Choose Color
                                <i class="fas fa-arrow-right" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Color Selection -->
                    <div class="form-section" id="step-color" data-step="3">
                        <div class="section-header">
                            <h3>
                                <i class="fas fa-palette" aria-hidden="true"></i>
                                Select Your Color
                            </h3>
                            <p class="section-description">Choose from our curated color palette. Colors may vary slightly based on fabric choice.</p>
                        </div>

                        <div class="color-options" id="color-options" role="radiogroup" aria-label="Color selection">
                            <!-- Color options will be populated dynamically -->
                        </div>

                        <div class="color-preview">
                            <div class="preview-card">
                                <div class="color-swatch" id="selected-color-preview"></div>
                                <div class="color-info">
                                    <h4 id="color-name">Select a color</h4>
                                    <p id="color-description">Choose your preferred color to see it here</p>
                                </div>
                            </div>
                        </div>

                        <div class="step-navigation">
                            <button type="button" class="btn-secondary" onclick="previousStep()">
                                <i class="fas fa-arrow-left" aria-hidden="true"></i>
                                Previous
                            </button>
                            <button type="button" class="btn-primary" onclick="nextStep()" disabled id="color-next">
                                Next: Choose Size
                                <i class="fas fa-arrow-right" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 3: Size Selection -->
                    <div class="form-section" id="step-size" data-step="4">
                        <div class="section-header">
                            <h3>
                                <i class="fas fa-ruler-combined" aria-hidden="true"></i>
                                Select Base Size
                            </h3>
                            <p class="section-description">Choose your base size. We'll fine-tune the fit with custom measurements in the next step.</p>
                        </div>

                        <div class="size-options" id="size-options" role="radiogroup" aria-label="Size selection">
                            <!-- Size options will be populated dynamically -->
                        </div>

                        <div class="size-guide">
                            <button type="button" class="btn-link" onclick="openSizeGuide()">
                                <i class="fas fa-info-circle" aria-hidden="true"></i>
                                View Size Guide
                            </button>
                        </div>

                        <div class="step-navigation">
                            <button type="button" class="btn-secondary" onclick="previousStep()">
                                <i class="fas fa-arrow-left" aria-hidden="true"></i>
                                Previous
                            </button>
                            <button type="button" class="btn-primary" onclick="nextStep()" disabled id="size-next">
                                Next: Measurements
                                <i class="fas fa-arrow-right" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 4: Measurements -->
                    <div class="form-section" id="step-measurements" data-step="5">
                        <div class="section-header">
                            <h3>
                                <i class="fas fa-ruler" aria-hidden="true"></i>
                                Custom Measurements
                            </h3>
                            <p class="section-description">Provide your measurements for a perfect fit. Need help? Check our measurement guide below.</p>
                        </div>

                        <div class="measurement-helper">
                            <div class="helper-tabs">
                                <button type="button" class="helper-tab active" data-tab="manual">
                                    <i class="fas fa-ruler" aria-hidden="true"></i>
                                    Manual Entry
                                </button>
                                <button type="button" class="helper-tab" data-tab="saved">
                                    <i class="fas fa-history" aria-hidden="true"></i>
                                    Saved Measurements
                                </button>
                                <button type="button" class="helper-tab" data-tab="guide">
                                    <i class="fas fa-question-circle" aria-hidden="true"></i>
                                    How to Measure
                                </button>
                            </div>
                        </div>

                        <div class="measurement-content">
                            <!-- Manual Entry Tab -->
                            <div class="tab-content active" id="manual-tab">
                                <div class="measurements-grid">
                                    <div class="measurement-group">
                                        <label for="bust">
                                            <span class="label-text">Bust</span>
                                            <span class="label-unit">(inches)</span>
                                            <i class="fas fa-info-circle measurement-tip" data-tip="Measure around the fullest part of your bust"></i>
                                        </label>
                                        <div class="input-wrapper">
                                            <input type="number" id="bust" name="bust" step="0.5" min="28" max="60" required
                                                   placeholder="e.g., 34.5" aria-describedby="bust-help">
                                            <span class="input-unit">in</span>
                                        </div>
                                        <div class="input-help" id="bust-help">Typical range: 28-60 inches</div>
                                    </div>

                                    <div class="measurement-group">
                                        <label for="waist">
                                            <span class="label-text">Waist</span>
                                            <span class="label-unit">(inches)</span>
                                            <i class="fas fa-info-circle measurement-tip" data-tip="Measure around your natural waistline"></i>
                                        </label>
                                        <div class="input-wrapper">
                                            <input type="number" id="waist" name="waist" step="0.5" min="22" max="50" required
                                                   placeholder="e.g., 28.0" aria-describedby="waist-help">
                                            <span class="input-unit">in</span>
                                        </div>
                                        <div class="input-help" id="waist-help">Typical range: 22-50 inches</div>
                                    </div>

                                    <div class="measurement-group">
                                        <label for="hips">
                                            <span class="label-text">Hips</span>
                                            <span class="label-unit">(inches)</span>
                                            <i class="fas fa-info-circle measurement-tip" data-tip="Measure around the fullest part of your hips"></i>
                                        </label>
                                        <div class="input-wrapper">
                                            <input type="number" id="hips" name="hips" step="0.5" min="30" max="60" required
                                                   placeholder="e.g., 36.5" aria-describedby="hips-help">
                                            <span class="input-unit">in</span>
                                        </div>
                                        <div class="input-help" id="hips-help">Typical range: 30-60 inches</div>
                                    </div>

                                    <div class="measurement-group">
                                        <label for="length">
                                            <span class="label-text">Dress Length</span>
                                            <span class="label-unit">(inches)</span>
                                            <i class="fas fa-info-circle measurement-tip" data-tip="Measure from shoulder to desired hem length"></i>
                                        </label>
                                        <div class="input-wrapper">
                                            <input type="number" id="length" name="length" step="0.5" min="30" max="70" required
                                                   placeholder="e.g., 42.0" aria-describedby="length-help">
                                            <span class="input-unit">in</span>
                                        </div>
                                        <div class="input-help" id="length-help">Typical range: 30-70 inches</div>
                                    </div>

                                    <div class="measurement-group">
                                        <label for="shoulder">
                                            <span class="label-text">Shoulder Width</span>
                                            <span class="label-unit">(inches)</span>
                                            <i class="fas fa-info-circle measurement-tip" data-tip="Measure from shoulder point to shoulder point"></i>
                                        </label>
                                        <div class="input-wrapper">
                                            <input type="number" id="shoulder" name="shoulder" step="0.5" min="12" max="20" required
                                                   placeholder="e.g., 15.5" aria-describedby="shoulder-help">
                                            <span class="input-unit">in</span>
                                        </div>
                                        <div class="input-help" id="shoulder-help">Typical range: 12-20 inches</div>
                                    </div>

                                    <div class="measurement-group">
                                        <label for="sleeve">
                                            <span class="label-text">Sleeve Length</span>
                                            <span class="label-unit">(inches)</span>
                                            <i class="fas fa-info-circle measurement-tip" data-tip="Measure from shoulder to wrist (optional)"></i>
                                        </label>
                                        <div class="input-wrapper">
                                            <input type="number" id="sleeve" name="sleeve" step="0.5" min="4" max="30"
                                                   placeholder="e.g., 24.0" aria-describedby="sleeve-help">
                                            <span class="input-unit">in</span>
                                        </div>
                                        <div class="input-help" id="sleeve-help">Optional - Leave blank for sleeveless</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Saved Measurements Tab -->
                            <div class="tab-content" id="saved-tab">
                                <div class="saved-measurements" id="saved-measurements-list">
                                    <p class="no-saved">No saved measurements found. Enter your measurements manually and save them for future orders.</p>
                                </div>
                            </div>

                            <!-- Measurement Guide Tab -->
                            <div class="tab-content" id="guide-tab">
                                <div class="measurement-guide">
                                    <div class="guide-section">
                                        <h4>How to Take Accurate Measurements</h4>
                                        <ol class="guide-steps">
                                            <li>Use a flexible measuring tape</li>
                                            <li>Wear well-fitting undergarments</li>
                                            <li>Stand straight with arms at your sides</li>
                                            <li>Take measurements over thin clothing</li>
                                            <li>Don't pull the tape too tight or too loose</li>
                                        </ol>
                                    </div>
                                    <div class="guide-video">
                                        <button type="button" class="btn-secondary" onclick="openMeasurementVideo()">
                                            <i class="fas fa-play" aria-hidden="true"></i>
                                            Watch Measurement Guide
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="measurement-actions">
                            <button type="button" class="btn-secondary" onclick="saveMeasurements()">
                                <i class="fas fa-save" aria-hidden="true"></i>
                                Save Measurements
                            </button>
                            <button type="button" class="btn-link" onclick="clearMeasurements()">
                                <i class="fas fa-undo" aria-hidden="true"></i>
                                Clear All
                            </button>
                        </div>

                        <div class="step-navigation">
                            <button type="button" class="btn-secondary" onclick="previousStep()">
                                <i class="fas fa-arrow-left" aria-hidden="true"></i>
                                Previous
                            </button>
                            <button type="button" class="btn-primary" onclick="nextStep()" disabled id="measurements-next">
                                Next: Delivery Options
                                <i class="fas fa-arrow-right" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Delivery Options -->
                    <div class="form-section">
                        <h3>Delivery Options</h3>
                        <div class="delivery-options">
                            <label class="delivery-option">
                                <input type="radio" name="delivery" value="express" required>
                                <div class="delivery-card">
                                    <h4>Express Delivery</h4>
                                    <p>4-6 hours</p>
                                    <span class="delivery-price">+₹500</span>
                                </div>
                            </label>
                            <label class="delivery-option">
                                <input type="radio" name="delivery" value="standard" required>
                                <div class="delivery-card">
                                    <h4>Standard Delivery</h4>
                                    <p>12-24 hours</p>
                                    <span class="delivery-price">+₹200</span>
                                </div>
                            </label>
                            <label class="delivery-option">
                                <input type="radio" name="delivery" value="regular" required>
                                <div class="delivery-card">
                                    <h4>Regular Delivery</h4>
                                    <p>24-48 hours</p>
                                    <span class="delivery-price">Free</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Address Selection -->
                    <div class="form-section">
                        <h3>Delivery Address</h3>
                        <div class="address-selection">
                            <div id="saved-addresses">
                                <!-- Saved addresses will be populated here -->
                            </div>
                            <button type="button" class="btn-secondary" onclick="addNewAddress()">
                                <i class="fas fa-plus"></i> Add New Address
                            </button>
                        </div>
                    </div>

                    <!-- Special Instructions -->
                    <div class="form-section">
                        <h3>Special Instructions</h3>
                        <textarea id="instructions" name="instructions" rows="4" 
                                  placeholder="Any special requirements, design preferences, or additional notes..."></textarea>
                    </div>

                    <!-- Order Summary -->
                    <div class="form-section">
                        <h3>Order Summary</h3>
                        <div class="order-summary">
                            <div class="summary-row">
                                <span>Base Price:</span>
                                <span>₹<span id="summary-base">0</span></span>
                            </div>
                            <div class="summary-row">
                                <span>Fabric Upgrade:</span>
                                <span>₹<span id="summary-fabric">0</span></span>
                            </div>
                            <div class="summary-row">
                                <span>Delivery:</span>
                                <span>₹<span id="summary-delivery">0</span></span>
                            </div>
                            <div class="summary-row total">
                                <span>Total:</span>
                                <span>₹<span id="summary-total">0</span></span>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="cta-button" style="width: 100%; margin-top: 2rem;">
                        <i class="fas fa-shopping-cart"></i> Place Order
                    </button>
                </form>
            </div>
        </div>
    </main>

    <!-- Address Modal -->
    <div id="address-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeAddressModal()">&times;</span>
            <h2>Add New Address</h2>
            <form id="address-form">
                <div class="form-group">
                    <label for="address-name">Address Name (e.g., Home, Office)</label>
                    <input type="text" id="address-name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="address-line1">Address Line 1</label>
                    <input type="text" id="address-line1" name="line1" required>
                </div>
                <div class="form-group">
                    <label for="address-line2">Address Line 2</label>
                    <input type="text" id="address-line2" name="line2">
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="address-city">City</label>
                        <input type="text" id="address-city" name="city" required>
                    </div>
                    <div class="form-group">
                        <label for="address-state">State</label>
                        <input type="text" id="address-state" name="state" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="address-pincode">Pincode</label>
                        <input type="text" id="address-pincode" name="pincode" pattern="[0-9]{6}" required>
                    </div>
                    <div class="form-group">
                        <label for="address-phone">Phone</label>
                        <input type="tel" id="address-phone" name="phone" pattern="[0-9]{10}" required>
                    </div>
                </div>
                <button type="submit" class="cta-button">Save Address</button>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="auth.js"></script>
    <script>
        let selectedDress = null;
        let userData = null;
        let savedAddresses = [];
        let savedMeasurements = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadUserData();
            loadSelectedDress();
            loadSavedData();
            setupEventListeners();
            updatePrice();
        });

        // Load user data
        function loadUserData() {
            userData = JSON.parse(localStorage.getItem('userData'));
            if (!userData) {
                window.location.href = 'login.html';
                return;
            }
        }

        // Load selected dress
        function loadSelectedDress() {
            selectedDress = JSON.parse(localStorage.getItem('selectedDress'));
            if (!selectedDress) {
                window.location.href = 'index.html';
                return;
            }

            document.getElementById('dress-image').src = selectedDress.image;
            document.getElementById('dress-name').textContent = selectedDress.name;
            document.getElementById('dress-description').textContent = selectedDress.description;
            document.getElementById('dress-price').textContent = selectedDress.price;

            populateOptions();
        }

        // Populate fabric, color, and size options
        function populateOptions() {
            // Fabric options
            const fabricOptions = document.getElementById('fabric-options');
            fabricOptions.innerHTML = selectedDress.fabric.map(fabric => `
                <label class="option-card">
                    <input type="radio" name="fabric" value="${fabric}" checked>
                    <span>${fabric}</span>
                </label>
            `).join('');

            // Color options
            const colorOptions = document.getElementById('color-options');
            colorOptions.innerHTML = selectedDress.colors.map(color => `
                <label class="option-card color-option">
                    <input type="radio" name="color" value="${color}" checked>
                    <span style="background-color: ${getColorHex(color)}"></span>
                    <span>${color}</span>
                </label>
            `).join('');

            // Size options
            const sizeOptions = document.getElementById('size-options');
            sizeOptions.innerHTML = selectedDress.sizes.map(size => `
                <label class="option-card">
                    <input type="radio" name="size" value="${size}" checked>
                    <span>${size}</span>
                </label>
            `).join('');
        }

        // Get color hex values
        function getColorHex(color) {
            const colorMap = {
                'Red': '#e74c3c',
                'Blue': '#3498db',
                'Green': '#27ae60',
                'Yellow': '#f1c40f',
                'Black': '#2c3e50',
                'White': '#ecf0f1',
                'Pink': '#ff69b4',
                'Purple': '#9b59b6',
                'Orange': '#e67e22',
                'Maroon': '#8b0000',
                'Gold': '#ffd700',
                'Silver': '#c0c0c0',
                'Navy': '#000080',
                'Teal': '#008080',
                'Grey': '#7f8c8d'
            };
            return colorMap[color] || '#95a5a6';
        }

        // Load saved data
        async function loadSavedData() {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/auth/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const profile = await response.json();
                    savedAddresses = profile.addresses || [];
                    savedMeasurements = profile.measurements || null;
                    
                    displaySavedAddresses();
                    
                    if (savedMeasurements) {
                        loadSavedMeasurements();
                    }
                }
            } catch (error) {
                console.error('Error loading saved data:', error);
            }
        }

        // Display saved addresses
        function displaySavedAddresses() {
            const container = document.getElementById('saved-addresses');
            if (savedAddresses.length > 0) {
                container.innerHTML = savedAddresses.map((address, index) => `
                    <label class="address-card">
                        <input type="radio" name="address" value="${index}" ${index === 0 ? 'checked' : ''}>
                        <div>
                            <strong>${address.name}</strong><br>
                            ${address.line1}, ${address.line2 || ''}<br>
                            ${address.city}, ${address.state} - ${address.pincode}<br>
                            Phone: ${address.phone}
                        </div>
                    </label>
                `).join('');
            } else {
                container.innerHTML = '<p>No saved addresses. Please add a new address.</p>';
            }
        }

        // Load saved measurements
        function loadSavedMeasurements() {
            if (savedMeasurements) {
                document.getElementById('bust').value = savedMeasurements.bust || '';
                document.getElementById('waist').value = savedMeasurements.waist || '';
                document.getElementById('hips').value = savedMeasurements.hips || '';
                document.getElementById('length').value = savedMeasurements.length || '';
                document.getElementById('shoulder').value = savedMeasurements.shoulder || '';
                document.getElementById('sleeve').value = savedMeasurements.sleeve || '';
            }
        }

        // Save measurements
        async function saveMeasurements() {
            const measurements = {
                bust: parseFloat(document.getElementById('bust').value),
                waist: parseFloat(document.getElementById('waist').value),
                hips: parseFloat(document.getElementById('hips').value),
                length: parseFloat(document.getElementById('length').value),
                shoulder: parseFloat(document.getElementById('shoulder').value),
                sleeve: parseFloat(document.getElementById('sleeve').value)
            };

            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/auth/profile/measurements', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(measurements)
                });

                if (response.ok) {
                    showMessage('Measurements saved successfully!', 'success');
                    savedMeasurements = measurements;
                } else {
                    showMessage('Failed to save measurements', 'error');
                }
            } catch (error) {
                console.error('Error saving measurements:', error);
                showMessage('Error saving measurements', 'error');
            }
        }

        // Add new address
        function addNewAddress() {
            document.getElementById('address-modal').style.display = 'block';
        }

        // Close address modal
        function closeAddressModal() {
            document.getElementById('address-modal').style.display = 'none';
        }

        // Address form submission
        document.getElementById('address-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const address = {
                name: document.getElementById('address-name').value,
                line1: document.getElementById('address-line1').value,
                line2: document.getElementById('address-line2').value,
                city: document.getElementById('address-city').value,
                state: document.getElementById('address-state').value,
                pincode: document.getElementById('address-pincode').value,
                phone: document.getElementById('address-phone').value
            };

            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/auth/profile/addresses', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(address)
                });

                if (response.ok) {
                    const newAddress = await response.json();
                    savedAddresses.push(newAddress);
                    displaySavedAddresses();
                    closeAddressModal();
                    document.getElementById('address-form').reset();
                    showMessage('Address added successfully!', 'success');
                } else {
                    showMessage('Failed to add address', 'error');
                }
            } catch (error) {
                console.error('Error adding address:', error);
                showMessage('Error adding address', 'error');
            }
        });

        // Update price based on selections
        function updatePrice() {
            let basePrice = selectedDress.price;
            let fabricPrice = 0;
            let deliveryPrice = 0;

            // Get selected delivery option
            const deliveryOption = document.querySelector('input[name="delivery"]:checked');
            if (deliveryOption) {
                switch (deliveryOption.value) {
                    case 'express':
                        deliveryPrice = 500;
                        break;
                    case 'standard':
                        deliveryPrice = 200;
                        break;
                    case 'regular':
                        deliveryPrice = 0;
                        break;
                }
            }

            const total = basePrice + fabricPrice + deliveryPrice;

            document.getElementById('summary-base').textContent = basePrice;
            document.getElementById('summary-fabric').textContent = fabricPrice;
            document.getElementById('summary-delivery').textContent = deliveryPrice;
            document.getElementById('summary-total').textContent = total;
        }

        // Setup event listeners
        function setupEventListeners() {
            // Update price on changes
            document.querySelectorAll('input[name="delivery"]').forEach(input => {
                input.addEventListener('change', updatePrice);
            });

            // Form submission with validation
            document.getElementById('customization-form').addEventListener('submit', async function(e) {
                e.preventDefault();

                if (validateCustomizationForm()) {
                    await placeOrder();
                }
            });
        }

        // Form validation function
        function validateCustomizationForm() {
            let isValid = true;
            const errors = [];

            // Check required measurements
            const requiredMeasurements = ['bust', 'waist', 'hips', 'length'];
            requiredMeasurements.forEach(field => {
                const value = document.getElementById(field).value;
                if (!value || parseFloat(value) <= 0) {
                    errors.push(`Please enter a valid ${field} measurement`);
                    isValid = false;
                }
            });

            // Check fabric selection
            const fabric = document.querySelector('input[name="fabric"]:checked');
            if (!fabric) {
                errors.push('Please select a fabric');
                isValid = false;
            }

            // Check color selection
            const color = document.querySelector('input[name="color"]:checked');
            if (!color) {
                errors.push('Please select a color');
                isValid = false;
            }

            // Check size selection
            const size = document.querySelector('input[name="size"]:checked');
            if (!size) {
                errors.push('Please select a size');
                isValid = false;
            }

            // Check delivery option
            const delivery = document.querySelector('input[name="delivery"]:checked');
            if (!delivery) {
                errors.push('Please select a delivery option');
                isValid = false;
            }

            // Check address selection
            const address = document.querySelector('input[name="address"]:checked');
            if (!address) {
                errors.push('Please select a delivery address');
                isValid = false;
            }

            if (!isValid) {
                showMessage(errors.join('<br>'), 'error');
            }

            return isValid;
        }

        // Enhanced place order function
        async function placeOrder() {
            const formData = new FormData(document.getElementById('customization-form'));

            // Show loading state
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing Order...';
            submitBtn.disabled = true;

            const orderData = {
                dressId: selectedDress.id,
                dressName: selectedDress.name,
                fabric: formData.get('fabric'),
                color: formData.get('color'),
                size: formData.get('size'),
                measurements: {
                    bust: parseFloat(document.getElementById('bust').value),
                    waist: parseFloat(document.getElementById('waist').value),
                    hips: parseFloat(document.getElementById('hips').value),
                    length: parseFloat(document.getElementById('length').value),
                    shoulder: parseFloat(document.getElementById('shoulder').value) || 0,
                    sleeve: parseFloat(document.getElementById('sleeve').value) || 0
                },
                delivery: formData.get('delivery'),
                address: savedAddresses[parseInt(formData.get('address'))],
                instructions: document.getElementById('instructions').value,
                totalPrice: parseInt(document.getElementById('summary-total').textContent),
                orderDate: new Date().toISOString(),
                status: 'pending'
            };

            try {
                // Get token from either storage
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');

                // For demo purposes, simulate successful order
                if (token && token.startsWith('demo-token')) {
                    // Demo order success
                    const demoOrder = {
                        id: 'ORD-' + Date.now(),
                        ...orderData,
                        estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                        trackingNumber: 'TRK-' + Math.random().toString(36).substr(2, 9).toUpperCase()
                    };

                    localStorage.setItem('orderSuccess', JSON.stringify(demoOrder));
                    showMessage('Order placed successfully! Redirecting...', 'success');

                    setTimeout(() => {
                        window.location.href = 'order-success.html';
                    }, 2000);

                } else {
                    // Try actual API call
                    const response = await fetch('/api/orders', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(orderData)
                    });

                    if (response.ok) {
                        const order = await response.json();
                        localStorage.setItem('orderSuccess', JSON.stringify(order));
                        showMessage('Order placed successfully! Redirecting...', 'success');

                        setTimeout(() => {
                            window.location.href = 'order-success.html';
                        }, 2000);
                    } else {
                        const errorData = await response.json();
                        throw new Error(errorData.message || 'Failed to place order');
                    }
                }
            } catch (error) {
                console.error('Error placing order:', error);
                showMessage(`Failed to place order: ${error.message}`, 'error');
            } finally {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }

        // Show message
        function showMessage(text, type = 'info') {
            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                ${text}
            `;
            
            message.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            
            document.body.appendChild(message);
            
            setTimeout(() => {
                message.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    message.remove();
                }, 300);
            }, 3000);
        }

        // Mobile menu functions
        function toggleMobileMenu() {
            const mobileNav = document.getElementById('mobile-nav');
            const mobileOverlay = document.getElementById('mobile-overlay');
            
            mobileNav.classList.toggle('active');
            mobileOverlay.classList.toggle('active');
            document.body.style.overflow = mobileNav.classList.contains('active') ? 'hidden' : 'auto';
        }

        function closeMobileMenu() {
            const mobileNav = document.getElementById('mobile-nav');
            const mobileOverlay = document.getElementById('mobile-overlay');
            
            mobileNav.classList.remove('active');
            mobileOverlay.classList.remove('active');
            document.body.style.overflow = 'auto';
        }
    </script>

    <style>
        .customization-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .customization-preview {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .dress-preview {
            display: flex;
            gap: 2rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .dress-preview img {
            width: 300px;
            height: 400px;
            object-fit: cover;
            border-radius: 10px;
        }

        .dress-info h2 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .price-display {
            font-size: 1.5rem;
            margin-top: 1rem;
        }

        .price-value {
            color: #ff6b9d;
            font-weight: bold;
        }

        .form-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #ff6b9d;
            padding-bottom: 0.5rem;
        }

        .fabric-options, .color-options, .size-options {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .option-card {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.2rem;
            border: 2px solid #ecf0f1;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .option-card:hover {
            border-color: #ff6b9d;
        }

        .option-card input[type="radio"] {
            display: none;
        }

        .option-card input[type="radio"]:checked + span {
            color: #ff6b9d;
            font-weight: bold;
        }

        .option-card:has(input[type="radio"]:checked) {
            border-color: #ff6b9d;
            background: rgba(255, 107, 157, 0.1);
        }

        .color-option span:first-child {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #ecf0f1;
        }

        .measurements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .measurement-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .measurement-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
        }

        .measurement-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .delivery-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .delivery-option {
            display: block;
            cursor: pointer;
        }

        .delivery-option input[type="radio"] {
            display: none;
        }

        .delivery-card {
            padding: 1.5rem;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .delivery-option input[type="radio"]:checked + .delivery-card {
            border-color: #ff6b9d;
            background: rgba(255, 107, 157, 0.1);
        }

        .delivery-card h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .delivery-price {
            color: #ff6b9d;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .address-card {
            display: block;
            padding: 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .address-card:has(input[type="radio"]:checked) {
            border-color: #ff6b9d;
            background: rgba(255, 107, 157, 0.1);
        }

        .order-summary {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .summary-row.total {
            font-size: 1.2rem;
            font-weight: bold;
            color: #ff6b9d;
            border-top: 2px solid #ecf0f1;
            padding-top: 0.5rem;
            margin-top: 0.5rem;
        }

        textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ecf0f1;
            border-radius: 8px;
            font-family: 'Poppins', sans-serif;
            resize: vertical;
        }

        @media (max-width: 768px) {
            .dress-preview {
                flex-direction: column;
                text-align: center;
            }

            .dress-preview img {
                width: 100%;
                max-width: 300px;
            }

            .measurements-grid {
                grid-template-columns: 1fr;
            }

            .delivery-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>