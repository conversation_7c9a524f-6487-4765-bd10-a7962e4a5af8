<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <PERSON><PERSON>'s Couture</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1>Admin Dashboard</h1>
                <p><PERSON><PERSON>'s Couture Management</p>
            </div>
            <nav class="nav-menu">
                <a href="#dashboard" class="nav-link active">Dashboard</a>
                <a href="#orders" class="nav-link">Orders</a>
                <a href="#staff" class="nav-link">Staff</a>
                <a href="#customers" class="nav-link">Customers</a>
                <a href="#analytics" class="nav-link">Analytics</a>
                <button id="logout-btn" class="btn-login">Logout</button>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            <!-- Dashboard Overview -->
            <section id="dashboard" class="admin-section">
                <h2>Dashboard Overview</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <i class="fas fa-shopping-cart"></i>
                        <div>
                            <h3 id="total-orders">0</h3>
                            <p>Total Orders</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-clock"></i>
                        <div>
                            <h3 id="pending-orders">0</h3>
                            <p>Pending Orders</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <h3 id="completed-orders">0</h3>
                            <p>Completed Orders</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-rupee-sign"></i>
                        <div>
                            <h3 id="total-revenue">₹0</h3>
                            <p>Total Revenue</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-users"></i>
                        <div>
                            <h3 id="total-customers">0</h3>
                            <p>Total Customers</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-user-tie"></i>
                        <div>
                            <h3 id="total-staff">0</h3>
                            <p>Total Staff</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Recent Orders -->
            <section id="orders" class="admin-section">
                <div class="section-header">
                    <h2>Recent Orders</h2>
                    <button class="btn-primary" onclick="refreshOrders()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Dress</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table-body">
                            <!-- Orders will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Staff Management -->
            <section id="staff" class="admin-section">
                <div class="section-header">
                    <h2>Staff Management</h2>
                    <button class="btn-primary" onclick="openAddStaffModal()">
                        <i class="fas fa-plus"></i> Add Staff
                    </button>
                </div>
                <div class="table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="staff-table-body">
                            <!-- Staff will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Customer Management -->
            <section id="customers" class="admin-section">
                <div class="section-header">
                    <h2>Customer Management</h2>
                </div>
                <div class="table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Orders</th>
                                <th>Total Spent</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="customers-table-body">
                            <!-- Customers will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>
        </div>
    </main>

    <!-- Add Staff Modal -->
    <div id="add-staff-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('add-staff-modal')">&times;</span>
            <h2>Add New Staff Member</h2>
            <form id="add-staff-form">
                <div class="form-group">
                    <label for="staff-name">Full Name *</label>
                    <input type="text" id="staff-name" required>
                </div>
                <div class="form-group">
                    <label for="staff-email">Email *</label>
                    <input type="email" id="staff-email" required>
                </div>
                <div class="form-group">
                    <label for="staff-phone">Phone *</label>
                    <input type="tel" id="staff-phone" required>
                </div>
                <div class="form-group">
                    <label for="staff-role">Role *</label>
                    <select id="staff-role" required>
                        <option value="staff">Staff</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="staff-password">Password *</label>
                    <input type="password" id="staff-password" required minlength="6">
                </div>
                <button type="submit" class="btn-primary">Add Staff Member</button>
            </form>
        </div>
    </div>

    <!-- Edit Staff Modal -->
    <div id="edit-staff-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('edit-staff-modal')">&times;</span>
            <h2>Edit Staff Member</h2>
            <form id="edit-staff-form">
                <input type="hidden" id="edit-staff-id">
                <div class="form-group">
                    <label for="edit-staff-name">Full Name *</label>
                    <input type="text" id="edit-staff-name" required>
                </div>
                <div class="form-group">
                    <label for="edit-staff-email">Email *</label>
                    <input type="email" id="edit-staff-email" required>
                </div>
                <div class="form-group">
                    <label for="edit-staff-phone">Phone *</label>
                    <input type="tel" id="edit-staff-phone" required>
                </div>
                <div class="form-group">
                    <label for="edit-staff-role">Role *</label>
                    <select id="edit-staff-role" required>
                        <option value="staff">Staff</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <button type="submit" class="btn-primary">Update Staff Member</button>
            </form>
        </div>
    </div>

    <!-- Order Details Modal -->
    <div id="order-details-modal" class="modal">
        <div class="modal-content large">
            <span class="close" onclick="closeModal('order-details-modal')">&times;</span>
            <h2>Order Details</h2>
            <div id="order-details-content">
                <!-- Order details will be loaded here -->
            </div>
        </div>
    </div>

    <script src="auth.js"></script>
    <script src="admin.js"></script>
</body>
</html>