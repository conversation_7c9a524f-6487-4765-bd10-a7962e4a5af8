<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Orders - Tailored Perfection</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .orders-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .orders-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .order-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .order-id {
            font-weight: bold;
            color: #667eea;
        }

        .order-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-confirmed { background: #d1ecf1; color: #0c5460; }
        .status-in-progress { background: #d4edda; color: #155724; }
        .status-shipped { background: #cce5ff; color: #004085; }
        .status-delivered { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }

        .order-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .detail-section h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .detail-item {
            margin-bottom: 5px;
            color: #666;
        }

        .order-actions {
            margin-top: 15px;
            text-align: right;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-left: 10px;
            font-size: 14px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 64px;
            color: #e0e0e0;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .order-details {
                grid-template-columns: 1fr;
            }
            
            .order-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="orders-container">
        <div class="orders-header">
            <h1><i class="fas fa-list"></i> My Orders</h1>
            <p>Track your custom dress orders</p>
        </div>

        <div id="ordersList">
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <h3>No orders yet</h3>
                <p>Start creating your custom dress today!</p>
                <a href="new-order.html" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create New Order
                </a>
            </div>
        </div>
    </div>

    <script src="auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
            loadOrders();
        });

        async function checkAuthentication() {
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            if (!token) {
                window.location.href = 'login.html?redirect=orders.html';
                return;
            }
            console.log('Authentication check passed for orders page');
        }

        async function loadOrders() {
            try {
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');

                // Handle demo tokens
                if (token && token.startsWith('demo-token')) {
                    console.log('Loading demo orders');
                    const demoOrders = getDemoOrders();
                    displayOrders(demoOrders);
                    return;
                }

                // Try API call for non-demo tokens
                const response = await fetch('/api/orders/my-orders', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const orders = await response.json();
                    displayOrders(orders);
                } else {
                    console.error('Failed to load orders from API');
                    // Fallback to demo orders
                    const demoOrders = getDemoOrders();
                    displayOrders(demoOrders);
                }
            } catch (error) {
                console.error('Error loading orders:', error);
                // Fallback to demo orders
                const demoOrders = getDemoOrders();
                displayOrders(demoOrders);
            }
        }

        function getDemoOrders() {
            // Check if there's a recent order from localStorage
            const recentOrder = localStorage.getItem('lastOrder') || localStorage.getItem('orderSuccess');
            const orders = [];

            if (recentOrder) {
                try {
                    const orderData = JSON.parse(recentOrder);
                    orders.push(orderData);
                } catch (e) {
                    console.error('Error parsing recent order:', e);
                }
            }

            // Add some demo orders
            const demoOrders = [
                {
                    id: 'ORD-1703000001-DEMO',
                    orderNumber: 'ORD-1703000001-DEMO',
                    status: 'delivered',
                    orderDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
                    estimatedDelivery: new Date(Date.now() - 23 * 24 * 60 * 60 * 1000).toISOString(),
                    dress: {
                        name: 'Elegant Evening Gown',
                        image: 'https://images.unsplash.com/photo-1566479179817-c0b5b4b8b1cc?w=300&h=400&fit=crop'
                    },
                    customization: {
                        fabric: 'silk',
                        color: 'navy'
                    },
                    pricing: {
                        totalPrice: 4500
                    },
                    trackingNumber: 'TRK-DEMO001'
                },
                {
                    id: 'ORD-1703000002-DEMO',
                    orderNumber: 'ORD-1703000002-DEMO',
                    status: 'in_progress',
                    orderDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                    estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                    dress: {
                        name: 'Traditional Saree Blouse',
                        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=300&h=400&fit=crop'
                    },
                    customization: {
                        fabric: 'cotton',
                        color: 'burgundy'
                    },
                    pricing: {
                        totalPrice: 2800
                    },
                    trackingNumber: 'TRK-DEMO002'
                }
            ];

            return [...orders, ...demoOrders];
        }

        function displayOrders(orders) {
            const ordersList = document.getElementById('ordersList');
            
            if (orders.length === 0) {
                ordersList.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h3>No orders yet</h3>
                        <p>Start creating your custom dress today!</p>
                        <a href="new-order.html" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create New Order
                        </a>
                    </div>
                `;
                return;
            }

            ordersList.innerHTML = '';
            
            orders.forEach(order => {
                const orderCard = createOrderCard(order);
                ordersList.appendChild(orderCard);
            });
        }

        function createOrderCard(order) {
            const card = document.createElement('div');
            card.className = 'order-card';
            
            const statusClass = `status-${order.status}`;
            const formattedDate = new Date(order.orderDate).toLocaleDateString();
            const deliveryDate = new Date(order.estimatedDelivery).toLocaleDateString();
            
            card.innerHTML = `
                <div class="order-header">
                    <div>
                        <span class="order-id">Order #${order._id.slice(-8)}</span>
                        <div style="font-size: 14px; color: #666; margin-top: 5px;">
                            Placed on ${formattedDate}
                        </div>
                    </div>
                    <span class="order-status ${statusClass}">${order.status.toUpperCase()}</span>
                </div>
                
                <div class="order-details">
                    <div class="detail-section">
                        <h4>Style Details</h4>
                        <div class="detail-item"><strong>Style:</strong> ${order.style}</div>
                        <div class="detail-item"><strong>Fabric:</strong> ${order.fabric}</div>
                        <div class="detail-item"><strong>Color:</strong> ${order.color}</div>
                        <div class="detail-item"><strong>Size:</strong> ${order.size}</div>
                    </div>
                    
                    <div class="detail-section">
                        <h4>Delivery Details</h4>
                        <div class="detail-item"><strong>Name:</strong> ${order.deliveryDetails.fullName}</div>
                        <div class="detail-item"><strong>Address:</strong> ${order.deliveryDetails.address}</div>
                        <div class="detail-item"><strong>City:</strong> ${order.deliveryDetails.city}</div>
                        <div class="detail-item"><strong>Est. Delivery:</strong> ${deliveryDate}</div>
                    </div>
                </div>
                
                <div class="order-actions">
                    <strong>Total: $${order.price.toFixed(2)}</strong>
                    <a href="order-details.html?id=${order._id}" class="btn btn-primary">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                </div>
            `;
            
            return card;
        }
    </script>
</body>
</html>