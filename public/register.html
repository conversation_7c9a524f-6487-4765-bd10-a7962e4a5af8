<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - <PERSON><PERSON>'s Couture</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .register-container {
            max-width: 1200px;
            width: 100%;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            min-height: 700px;
        }

        .register-content {
            display: flex;
            min-height: 700px;
        }

        .register-info {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 60px 40px;
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .register-info h1 {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .register-info p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .benefits-list {
            list-style: none;
        }

        .benefits-list li {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .benefits-list i {
            color: #ffd700;
            font-size: 1.2rem;
        }

        .register-form-container {
            flex: 1.5;
            padding: 60px 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .register-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .register-header h2 {
            font-family: 'Playfair Display', serif;
            font-size: 2.2rem;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .register-header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group .input-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 1rem;
        }

        .password-strength {
            margin-top: 5px;
            font-size: 0.8rem;
            color: #7f8c8d;
        }

        .password-strength.weak { color: #e74c3c; }
        .password-strength.medium { color: #f39c12; }
        .password-strength.strong { color: #27ae60; }

        .terms-checkbox {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            margin: 20px 0;
            font-size: 0.9rem;
            color: #7f8c8d;
        }

        .terms-checkbox input[type="checkbox"] {
            width: auto;
            margin-top: 3px;
            accent-color: #667eea;
        }

        .terms-checkbox a {
            color: #667eea;
            text-decoration: none;
        }

        .terms-checkbox a:hover {
            text-decoration: underline;
        }

        .register-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .register-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .register-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-link {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.95rem;
        }

        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .error-message, .success-message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 0.9rem;
        }

        .error-message {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.2);
        }

        .success-message {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            border: 1px solid rgba(39, 174, 96, 0.2);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-to-home {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #667eea;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
            transition: color 0.3s ease;
            z-index: 10;
        }

        .back-to-home:hover {
            color: #764ba2;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ecf0f1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #7f8c8d;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
        }

        .step.completed {
            background: #27ae60;
            color: white;
        }

        @media (max-width: 968px) {
            .register-content {
                flex-direction: column;
            }

            .register-info {
                padding: 40px 30px;
            }

            .register-info h1 {
                font-size: 2rem;
            }

            .register-form-container {
                padding: 40px 30px;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }

        @media (max-width: 480px) {
            .register-container {
                margin: 10px;
                border-radius: 15px;
            }

            .register-info, .register-form-container {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <a href="/" class="back-to-home">
        <i class="fas fa-arrow-left"></i> Back to Home
    </a>

    <div class="register-container">
        <div class="register-content">
            <div class="register-info">
                <h1>Join Nishi's Couture</h1>
                <p>Create your account to unlock personalized tailoring experiences with saved measurements, addresses, and exclusive offers.</p>
                
                <ul class="benefits-list">
                    <li><i class="fas fa-check-circle"></i> Save your measurements for quick ordering</li>
                    <li><i class="fas fa-check-circle"></i> Store multiple delivery addresses</li>
                    <li><i class="fas fa-check-circle"></i> Track your orders in real-time</li>
                    <li><i class="fas fa-check-circle"></i> Get exclusive member discounts</li>
                    <li><i class="fas fa-check-circle"></i> Receive personalized recommendations</li>
                </ul>
            </div>

            <div class="register-form-container">
                <div class="register-header">
                    <h2>Create Account</h2>
                    <p>Let's get you started</p>
                </div>

                <div class="step-indicator">
                    <div class="step active" id="step1">1</div>
                    <div class="step" id="step2">2</div>
                    <div class="step" id="step3">3</div>
                </div>

                <div id="errorMessage" class="error-message"></div>
                <div id="successMessage" class="success-message"></div>

                <form id="registerForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="fullName">Full Name *</label>
                            <input type="text" id="fullName" name="fullName" required placeholder="Enter your full name">
                            <i class="fas fa-user input-icon"></i>
                        </div>
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required placeholder="Enter your email">
                            <i class="fas fa-envelope input-icon"></i>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="phoneNumber">Phone Number *</label>
                            <input type="tel" id="phoneNumber" name="phoneNumber" required placeholder="+91 9876543210">
                            <i class="fas fa-phone input-icon"></i>
                        </div>
                        <div class="form-group">
                            <label for="dateOfBirth">Date of Birth</label>
                            <input type="date" id="dateOfBirth" name="dateOfBirth">
                            <i class="fas fa-calendar input-icon"></i>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="password">Password *</label>
                            <input type="password" id="password" name="password" required placeholder="Create a strong password">
                            <i class="fas fa-lock input-icon"></i>
                            <div class="password-strength" id="passwordStrength">Password strength: <span id="strengthText">Enter password</span></div>
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">Confirm Password *</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="Confirm your password">
                            <i class="fas fa-lock input-icon"></i>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="preferredSizeType">Preferred Size Type</label>
                            <select id="preferredSizeType" name="preferredSizeType">
                                <option value="">Select size type</option>
                                <option value="XS">XS - Extra Small</option>
                                <option value="S">S - Small</option>
                                <option value="M">M - Medium</option>
                                <option value="L">L - Large</option>
                                <option value="XL">XL - Extra Large</option>
                                <option value="XXL">XXL - Extra Extra Large</option>
                                <option value="Custom">Custom - I'll provide measurements</option>
                            </select>
                            <i class="fas fa-ruler input-icon"></i>
                        </div>
                    </div>

                    <div class="terms-checkbox">
                        <input type="checkbox" id="terms" name="terms" required>
                        <label for="terms">
                            I agree to the <a href="#" onclick="showTerms()">Terms of Service</a> and 
                            <a href="#" onclick="showPrivacy()">Privacy Policy</a>
                        </label>
                    </div>

                    <button type="submit" class="register-btn" id="registerBtn">
                        <span class="btn-text">Create Account</span>
                        <span class="loading-spinner" style="display: none;"></span>
                    </button>
                </form>

                <div class="login-link">
                    <p>Already have an account? <a href="login.html">Sign in here</a></p>
                </div>
            </div>
        </div>
    </div>

    <script src="auth.js"></script>
    <script>
        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthText = document.getElementById('strengthText');
            const strengthDiv = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthText.textContent = 'Enter password';
                strengthDiv.className = 'password-strength';
            } else if (password.length < 6) {
                strengthText.textContent = 'Weak';
                strengthDiv.className = 'password-strength weak';
            } else if (password.length < 10 || !/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
                strengthText.textContent = 'Medium';
                strengthDiv.className = 'password-strength medium';
            } else {
                strengthText.textContent = 'Strong';
                strengthDiv.className = 'password-strength strong';
            }
        });

        // Password confirmation check
        document.getElementById('confirmPassword').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });

        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            const registerBtn = document.getElementById('registerBtn');
            const btnText = registerBtn.querySelector('.btn-text');
            const spinner = registerBtn.querySelector('.loading-spinner');
            
            // Validate terms
            if (!document.getElementById('terms').checked) {
                showMessage('Please accept the terms and conditions', 'error');
                return;
            }
            
            // Show loading state
            registerBtn.disabled = true;
            btnText.style.display = 'none';
            spinner.style.display = 'inline-block';
            
            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showMessage('Account created successfully! Redirecting to login...', 'success');
                    
                    // Redirect to login with email pre-filled
                    setTimeout(() => {
                        window.location.href = `login.html?email=${encodeURIComponent(data.email)}`;
                    }, 2000);
                } else {
                    showMessage(result.error || 'Registration failed', 'error');
                }
            } catch (error) {
                showMessage('Network error. Please try again.', 'error');
            } finally {
                // Reset button state
                registerBtn.disabled = false;
                btnText.style.display = 'inline';
                spinner.style.display = 'none';
            }
        });

        function showMessage(message, type) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');
            
            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';
            
            const targetDiv = type === 'error' ? errorDiv : successDiv;
            targetDiv.textContent = message;
            targetDiv.style.display = 'block';
            
            setTimeout(() => {
                targetDiv.style.display = 'none';
            }, 5000);
        }

        function showTerms() {
            alert('Terms of Service: By creating an account, you agree to our terms of service, privacy policy, and to receive promotional emails.');
        }

        function showPrivacy() {
            alert('Privacy Policy: We respect your privacy and will never share your personal information with third parties.');
        }

        // Auto-format phone number
        document.getElementById('phoneNumber').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 0) {
                if (value.length <= 3) {
                    value = '+91 ' + value;
                } else if (value.length <= 7) {
                    value = '+91 ' + value.slice(0, 3) + ' ' + value.slice(3);
                } else {
                    value = '+91 ' + value.slice(0, 3) + ' ' + value.slice(3, 7) + ' ' + value.slice(7, 11);
                }
            }
            e.target.value = value;
        });
    </script>
</body>
</html>