// Enhanced dress data with more details
const dressesData = [
    {
        id: 1,
        name: "Elegant Saree Blouse",
        category: "blouse",
        price: 1200,
        basePrice: 1200,
        urgency: "high",
        image: "https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=300",
        features: ["Custom Fit", "Same Day", "Premium Fabric"],
        rating: 4.8,
        reviews: 124,
        description: "Beautifully crafted saree blouse with intricate embroidery work",
        fabric: ["Silk", "Cotton", "Georgette"],
        colors: ["Red", "Gold", "Maroon", "Pink"],
        sizes: ["XS", "S", "M", "L", "XL", "XXL"],
        deliveryTime: { express: "4-6 hours", standard: "12-24 hours", regular: "24-48 hours" }
    },
    {
        id: 2,
        name: "Designer Kurti",
        category: "kurti",
        price: 800,
        basePrice: 800,
        urgency: "medium",
        image: "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=300",
        features: ["Cotton", "Comfortable", "Trendy"],
        rating: 4.5,
        reviews: 89,
        description: "Comfortable cotton kurti perfect for daily wear",
        fabric: ["Cotton", "Rayon", "Linen"],
        colors: ["Blue", "White", "Pink", "Yellow", "Green"],
        sizes: ["S", "M", "L", "XL", "XXL"],
        deliveryTime: { express: "6-8 hours", standard: "24 hours", regular: "48 hours" }
    },
    {
        id: 3,
        name: "Party Wear Dress",
        category: "dress",
        price: 2500,
        basePrice: 2500,
        urgency: "high",
        image: "https://images.unsplash.com/photo-1566479179817-c0b5b4b4b1e5?w=300",
        features: ["Evening Wear", "Luxury", "Custom Design"],
        rating: 4.9,
        reviews: 67,
        description: "Stunning party dress with premium fabric and elegant design",
        fabric: ["Silk", "Chiffon", "Georgette", "Satin"],
        colors: ["Black", "Navy", "Maroon", "Gold", "Silver"],
        sizes: ["XS", "S", "M", "L", "XL"],
        deliveryTime: { express: "4-6 hours", standard: "12 hours", regular: "24 hours" }
    },
    {
        id: 4,
        name: "Traditional Saree",
        category: "saree",
        price: 1800,
        basePrice: 1800,
        urgency: "low",
        image: "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=300",
        features: ["Handwoven", "Traditional", "Elegant"],
        rating: 4.7,
        reviews: 156,
        description: "Authentic handwoven saree with traditional patterns",
        fabric: ["Silk", "Cotton", "Handloom"],
        colors: ["Red", "Green", "Blue", "Purple", "Orange"],
        sizes: ["One Size"],
        deliveryTime: { express: "8 hours", standard: "24 hours", regular: "48 hours" }
    },
    {
        id: 5,
        name: "Casual Kurti",
        category: "kurti",
        price: 600,
        basePrice: 600,
        urgency: "medium",
        image: "https://images.unsplash.com/photo-1564257577-4b0b5b1b1b1b?w=300",
        features: ["Daily Wear", "Comfortable", "Affordable"],
        rating: 4.3,
        reviews: 203,
        description: "Simple and comfortable kurti for everyday wear",
        fabric: ["Cotton", "Rayon"],
        colors: ["White", "Blue", "Pink", "Yellow", "Grey"],
        sizes: ["S", "M", "L", "XL", "XXL"],
        deliveryTime: { express: "6 hours", standard: "24 hours", regular: "48 hours" }
    },
    {
        id: 6,
        name: "Wedding Blouse",
        category: "blouse",
        price: 3000,
        basePrice: 3000,
        urgency: "high",
        image: "https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=300",
        features: ["Bridal", "Heavy Work", "Premium"],
        rating: 4.9,
        reviews: 45,
        description: "Exquisite bridal blouse with heavy embroidery and premium finish",
        fabric: ["Silk", "Brocade", "Velvet"],
        colors: ["Gold", "Red", "Maroon", "Pink", "Orange"],
        sizes: ["XS", "S", "M", "L", "XL"],
        deliveryTime: { express: "4-6 hours", standard: "12 hours", regular: "24 hours" }
    },
    {
        id: 7,
        name: "Indo-Western Dress",
        category: "dress",
        price: 1500,
        basePrice: 1500,
        urgency: "medium",
        image: "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=300",
        features: ["Fusion", "Modern", "Stylish"],
        rating: 4.6,
        reviews: 78,
        description: "Perfect blend of Indian and Western styles",
        fabric: ["Georgette", "Crepe", "Cotton"],
        colors: ["Black", "Navy", "Maroon", "Teal", "Purple"],
        sizes: ["S", "M", "L", "XL"],
        deliveryTime: { express: "6 hours", standard: "24 hours", regular: "48 hours" }
    },
    {
        id: 8,
        name: "Festive Kurti",
        category: "kurti",
        price: 1200,
        basePrice: 1200,
        urgency: "medium",
        image: "https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=300",
        features: ["Festive", "Embroidered", "Premium"],
        rating: 4.7,
        reviews: 92,
        description: "Beautiful festive kurti with intricate embroidery",
        fabric: ["Silk", "Georgette", "Cotton"],
        colors: ["Red", "Green", "Blue", "Pink", "Orange"],
        sizes: ["S", "M", "L", "XL", "XXL"],
        deliveryTime: { express: "6 hours", standard: "24 hours", regular: "48 hours" }
    }
];

// Global variables
let filteredDresses = [...dressesData];
let currentPage = 1;
let itemsPerPage = 6;
let currentFilters = {
    category: 'all',
    priceRange: 5000,
    delivery: 'all',
    sort: 'featured'
};
let currentView = 'grid';
let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

// DOM Elements
const dressesGrid = document.getElementById('dresses-grid');

// Enhanced initialization with loading states and error handling
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

async function initializeApp() {
    try {
        showLoadingState();

        // Initialize core functionality
        await Promise.all([
            setupEnhancedFilters(),
            checkLoginState(),
            initializeAnimations(),
            setupAccessibility()
        ]);

        applyFilters();
        setupEventListeners();
        updateUIForAuth();
        hideLoadingState();

        // Show welcome animation
        animatePageLoad();

    } catch (error) {
        console.error('Failed to initialize app:', error);
        showMessage('Failed to load the application. Please refresh the page.', 'error');
        hideLoadingState();
    }
}

function showLoadingState() {
    const loadingElement = document.querySelector('.loading-skeleton');
    if (loadingElement) {
        loadingElement.style.display = 'grid';
    }

    // Update collection count
    const collectionCount = document.getElementById('collection-count');
    if (collectionCount) {
        collectionCount.textContent = 'Loading collection...';
    }
}

function hideLoadingState() {
    const loadingElement = document.querySelector('.loading-skeleton');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

function initializeAnimations() {
    return new Promise((resolve) => {
        // Initialize intersection observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.feature-item, .dress-card, .stat-item').forEach(el => {
            observer.observe(el);
        });

        resolve();
    });
}

function setupAccessibility() {
    return new Promise((resolve) => {
        // Enhanced keyboard navigation
        document.addEventListener('keydown', handleKeyboardNavigation);

        // Focus management for modals
        setupFocusManagement();

        // Screen reader announcements
        setupScreenReaderSupport();

        resolve();
    });
}

function animatePageLoad() {
    // Animate hero elements
    const heroElements = document.querySelectorAll('.hero-title-line, .hero-description, .hero-features, .hero-cta');
    heroElements.forEach((el, index) => {
        setTimeout(() => {
            if (el) {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }
        }, index * 200);
    });

    // Animate stats counter
    animateCounters();
}

function animateCounters() {
    const counters = document.querySelectorAll('.stat-number[data-count]');
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-count'));
        let current = 0;
        const increment = target / 50;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 30);
    });
}

// Enhanced Accessibility Functions
function handleKeyboardNavigation(event) {
    // Handle escape key for modals
    if (event.key === 'Escape') {
        closeAllModals();
    }

    // Handle tab navigation for filter buttons
    if (event.key === 'Tab' && event.target.classList.contains('filter-btn')) {
        updateFilterFocus();
    }

    // Handle enter/space for custom elements
    if ((event.key === 'Enter' || event.key === ' ') && event.target.classList.contains('dress-card')) {
        event.preventDefault();
        event.target.click();
    }
}

function setupFocusManagement() {
    // Store focus before opening modals
    let lastFocusedElement = null;

    document.addEventListener('focusin', (event) => {
        if (!event.target.closest('.modal')) {
            lastFocusedElement = event.target;
        }
    });

    // Restore focus when modal closes
    document.addEventListener('modalClosed', () => {
        if (lastFocusedElement) {
            lastFocusedElement.focus();
        }
    });
}

function setupScreenReaderSupport() {
    // Create live region for announcements
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    liveRegion.id = 'live-region';
    document.body.appendChild(liveRegion);
}

function announceToScreenReader(message) {
    const liveRegion = document.getElementById('live-region');
    if (liveRegion) {
        liveRegion.textContent = message;
        setTimeout(() => {
            liveRegion.textContent = '';
        }, 1000);
    }
}

function closeAllModals() {
    const modals = document.querySelectorAll('.modal.active');
    modals.forEach(modal => {
        modal.classList.remove('active');
        document.dispatchEvent(new CustomEvent('modalClosed'));
    });
}

function updateFilterFocus() {
    const activeFilter = document.querySelector('.filter-btn.active');
    if (activeFilter) {
        activeFilter.setAttribute('aria-selected', 'true');
    }
}

// Enhanced Error Handling and User Feedback
function showMessage(message, type = 'info', duration = 5000) {
    const messageContainer = getOrCreateMessageContainer();

    const messageElement = document.createElement('div');
    messageElement.className = `message message-${type}`;
    messageElement.innerHTML = `
        <div class="message-content">
            <i class="fas fa-${getMessageIcon(type)}" aria-hidden="true"></i>
            <span>${message}</span>
        </div>
        <button class="message-close" onclick="closeMessage(this)" aria-label="Close message">
            <i class="fas fa-times" aria-hidden="true"></i>
        </button>
    `;

    messageContainer.appendChild(messageElement);

    // Auto-remove after duration
    setTimeout(() => {
        if (messageElement.parentNode) {
            messageElement.remove();
        }
    }, duration);

    // Announce to screen readers
    announceToScreenReader(message);
}

function getOrCreateMessageContainer() {
    let container = document.getElementById('message-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'message-container';
        container.className = 'message-container';
        document.body.appendChild(container);
    }
    return container;
}

function getMessageIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

function closeMessage(button) {
    const message = button.closest('.message');
    if (message) {
        message.remove();
    }
}

// Mobile Navigation Functions
function toggleMobileMenu() {
    const mobileNav = document.getElementById('mobile-nav');
    const mobileOverlay = document.getElementById('mobile-overlay');
    
    mobileNav.classList.toggle('active');
    mobileOverlay.classList.toggle('active');
    document.body.style.overflow = mobileNav.classList.contains('active') ? 'hidden' : 'auto';
}

function closeMobileMenu() {
    const mobileNav = document.getElementById('mobile-nav');
    const mobileOverlay = document.getElementById('mobile-overlay');
    
    mobileNav.classList.remove('active');
    mobileOverlay.classList.remove('active');
    document.body.style.overflow = 'auto';
}

// Enhanced Product Card Creation
function createEnhancedDressCard(dress) {
    const card = document.createElement('div');
    card.className = 'dress-card';
    
    const urgencyClass = dress.urgency === 'high' ? 'high' : 
                        dress.urgency === 'medium' ? 'medium' : 'low';
    
    const urgencyText = dress.urgency === 'high' ? 'Express' : 
                       dress.urgency === 'medium' ? 'Standard' : 'Regular';
    
    const isWishlisted = wishlist.includes(dress.id);
    const starsHtml = generateStars(dress.rating);
    
    card.innerHTML = `
        <div class="dress-image">
            <div class="urgency-badge ${urgencyClass}">${urgencyText}</div>
            <button class="wishlist-btn ${isWishlisted ? 'wishlisted' : ''}" onclick="toggleWishlist(${dress.id})">
                <i class="${isWishlisted ? 'fas' : 'far'} fa-heart"></i>
            </button>
            <button class="quick-view-btn" onclick="quickViewDress(${dress.id})">
                Quick View
            </button>
            <img src="${dress.image}" alt="${dress.name}" loading="lazy">
        </div>
        <div class="dress-info">
            <h3>${dress.name}</h3>
            <div class="dress-rating">
                <div class="stars">${starsHtml}</div>
                <span class="rating-text">${dress.rating} (${dress.reviews})</span>
            </div>
            <div class="dress-price">₹${dress.price}</div>
            <div class="dress-features">
                ${dress.features.map(feature => `<span class="feature-tag">${feature}</span>`).join('')}
            </div>
            <button class="order-btn" onclick="openCustomization(${dress.id})">
                <i class="fas fa-magic"></i> Customize & Order
            </button>
        </div>
    `;
    
    return card;
}

// Generate star rating HTML
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    
    let starsHtml = '';
    for (let i = 0; i < fullStars; i++) {
        starsHtml += '<i class="fas fa-star"></i>';
    }
    if (hasHalfStar) {
        starsHtml += '<i class="fas fa-star-half-alt"></i>';
    }
    for (let i = 0; i < emptyStars; i++) {
        starsHtml += '<i class="far fa-star"></i>';
    }
    
    return starsHtml;
}

// Enhanced Filter System
function setupEnhancedFilters() {
    // Category filter
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            currentFilters.category = this.value;
            applyFilters();
        });
    }
    
    // Price range filter
    const priceRange = document.getElementById('priceRange');
    const priceValue = document.getElementById('priceValue');
    if (priceRange && priceValue) {
        priceRange.addEventListener('input', function() {
            currentFilters.priceRange = this.value;
            priceValue.textContent = `₹${this.value}`;
            applyFilters();
        });
    }
    
    // Delivery filter
    const deliveryFilter = document.getElementById('deliveryFilter');
    if (deliveryFilter) {
        deliveryFilter.addEventListener('change', function() {
            currentFilters.delivery = this.value;
            applyFilters();
        });
    }
    
    // Sort buttons
    const sortBtns = document.querySelectorAll('.sort-btn');
    sortBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            sortBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentFilters.sort = this.dataset.sort;
            applyFilters();
        });
    });
    
    // Filter tabs (mobile friendly)
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentFilters.category = this.dataset.category;
            
            // Update dropdown too
            if (categoryFilter) {
                categoryFilter.value = this.dataset.category;
            }
            
            applyFilters();
        });
    });
    
    // View toggle
    const viewBtns = document.querySelectorAll('.view-btn');
    viewBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            viewBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentView = this.dataset.view;
            updateView();
        });
    });
}

// Apply all filters
function applyFilters() {
    let filtered = [...dressesData];
    
    // Category filter
    if (currentFilters.category !== 'all') {
        filtered = filtered.filter(dress => dress.category === currentFilters.category);
    }
    
    // Price filter
    filtered = filtered.filter(dress => dress.price <= currentFilters.priceRange);
    
    // Delivery filter
    if (currentFilters.delivery !== 'all') {
        const deliveryMap = { express: 'high', standard: 'medium', regular: 'low' };
        filtered = filtered.filter(dress => dress.urgency === deliveryMap[currentFilters.delivery]);
    }
    
    // Sort
    switch (currentFilters.sort) {
        case 'price-low':
            filtered.sort((a, b) => a.price - b.price);
            break;
        case 'price-high':
            filtered.sort((a, b) => b.price - a.price);
            break;
        case 'rating':
            filtered.sort((a, b) => b.rating - a.rating);
            break;
        case 'newest':
            filtered.sort((a, b) => b.id - a.id);
            break;
        default: // featured
            // Keep original order
            break;
    }
    
    filteredDresses = filtered;
    currentPage = 1;
    displayDresses();
    updateResultsInfo();
}

// Display dresses with pagination
function displayDresses() {
    if (!dressesGrid) return;

    // Show loading state briefly for better UX
    showDressesLoading();

    // Use requestAnimationFrame for smooth rendering
    requestAnimationFrame(() => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const dressesToShow = filteredDresses.slice(0, endIndex);

        dressesGrid.innerHTML = '';

        if (dressesToShow.length === 0) {
            renderNoResults();
        } else {
            // Create document fragment for better performance
            const fragment = document.createDocumentFragment();

            dressesToShow.forEach((dress, index) => {
                const dressCard = createEnhancedDressCard(dress);
                // Add staggered animation
                dressCard.style.animationDelay = `${index * 0.1}s`;
                dressCard.classList.add('fade-in');
                fragment.appendChild(dressCard);
            });

            dressesGrid.appendChild(fragment);

            // Update load more button
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            if (loadMoreBtn) {
                if (endIndex >= filteredDresses.length) {
                    loadMoreBtn.style.display = 'none';
                } else {
                    loadMoreBtn.style.display = 'block';
                    loadMoreBtn.setAttribute('aria-label', `Load more dresses. ${filteredDresses.length - endIndex} remaining`);
                }
            }

            // Initialize lazy loading for new images
            initializeLazyLoading();
        }

        // Update collection count
        updateCollectionCount(dressesToShow.length, filteredDresses.length);

        // Announce to screen readers
        announceToScreenReader(`${dressesToShow.length} dresses displayed`);
    });
}

// Enhanced helper functions
function showDressesLoading() {
    if (dressesGrid) {
        dressesGrid.innerHTML = `
            <div class="loading-dresses">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <p>Loading beautiful dresses...</p>
            </div>
        `;
    }
}

function renderNoResults() {
    dressesGrid.innerHTML = `
        <div class="no-results">
            <div class="no-results-icon">
                <i class="fas fa-search" aria-hidden="true"></i>
            </div>
            <h3>No dresses found</h3>
            <p>Try adjusting your filters or search terms to discover more options</p>
            <div class="no-results-actions">
                <button class="btn btn-secondary" onclick="clearAllFilters()">
                    <i class="fas fa-undo" aria-hidden="true"></i>
                    Clear All Filters
                </button>
                <button class="btn btn-primary" onclick="scrollToSection('contact')">
                    <i class="fas fa-envelope" aria-hidden="true"></i>
                    Request Custom Design
                </button>
            </div>
        </div>
    `;
}

function updateCollectionCount(showing, total) {
    const collectionCount = document.getElementById('collection-count');
    if (collectionCount) {
        const totalDresses = dressesData.length;
        if (total === totalDresses) {
            collectionCount.textContent = `Showing ${showing} of ${totalDresses} dresses`;
        } else {
            collectionCount.textContent = `Showing ${showing} of ${total} filtered dresses (${totalDresses} total)`;
        }
    }
}

function initializeLazyLoading() {
    const images = document.querySelectorAll('.dress-card img[data-src]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    img.classList.add('loaded');
                    imageObserver.unobserve(img);
                }
            });
        }, {
            rootMargin: '50px'
        });

        images.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback for older browsers
        images.forEach(img => {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            img.classList.add('loaded');
        });
    }
}

function clearAllFilters() {
    // Reset all filters to default
    currentFilters = {
        category: 'all',
        priceRange: 5000,
        delivery: 'all',
        sort: 'featured'
    };

    // Update UI
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
        btn.setAttribute('aria-selected', 'false');
    });

    const allFilterBtn = document.querySelector('.filter-btn[data-category="all"]');
    if (allFilterBtn) {
        allFilterBtn.classList.add('active');
        allFilterBtn.setAttribute('aria-selected', 'true');
    }

    // Reset current page
    currentPage = 1;

    // Apply filters
    applyFilters();

    // Show success message
    showMessage('All filters cleared successfully', 'success');

    // Announce to screen readers
    announceToScreenReader('Filters cleared, showing all dresses');
}

// Load more dresses
function loadMoreDresses() {
    currentPage++;
    displayDresses();

    // Announce to screen readers
    announceToScreenReader('More dresses loaded');
}

// Update results info
function updateResultsInfo() {
    const resultsCount = document.getElementById('resultsCount');
    if (resultsCount) {
        const showing = Math.min(currentPage * itemsPerPage, filteredDresses.length);
        resultsCount.textContent = `Showing ${showing} of ${filteredDresses.length} dresses`;
    }
}

// Update view (grid/list)
function updateView() {
    if (!dressesGrid) return;
    
    if (currentView === 'list') {
        dressesGrid.classList.add('list-view');
    } else {
        dressesGrid.classList.remove('list-view');
    }
}

// Clear all filters
function clearFilters() {
    currentFilters = {
        category: 'all',
        priceRange: 5000,
        delivery: 'all',
        sort: 'featured'
    };
    
    // Reset UI elements
    const categoryFilter = document.getElementById('categoryFilter');
    const priceRange = document.getElementById('priceRange');
    const priceValue = document.getElementById('priceValue');
    const deliveryFilter = document.getElementById('deliveryFilter');
    
    if (categoryFilter) categoryFilter.value = 'all';
    if (priceRange) {
        priceRange.value = 5000;
        priceValue.textContent = '₹5000';
    }
    if (deliveryFilter) deliveryFilter.value = 'all';
    
    // Reset sort buttons
    document.querySelectorAll('.sort-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.sort === 'featured') {
            btn.classList.add('active');
        }
    });
    
    // Reset filter tabs
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.category === 'all') {
            btn.classList.add('active');
        }
    });
    
    applyFilters();
}

// Enhanced Wishlist functionality
function toggleWishlist(dressId, buttonElement) {
    const index = wishlist.indexOf(dressId);
    const btn = buttonElement || document.querySelector(`[data-dress-id="${dressId}"] .wishlist-btn`);
    const icon = btn?.querySelector('i');
    
    if (index === -1) {
        wishlist.push(dressId);
        icon.className = 'fas fa-heart';
        btn.classList.add('wishlisted');
        showMessage('Added to wishlist!', 'success');
    } else {
        wishlist.splice(index, 1);
        icon.className = 'far fa-heart';
        btn.classList.remove('wishlisted');
        showMessage('Removed from wishlist', 'info');
    }
    
    localStorage.setItem('wishlist', JSON.stringify(wishlist));
}

// Quick view functionality
function quickViewDress(dressId) {
    const dress = dressesData.find(d => d.id === dressId);
    if (!dress) return;
    
    const quickViewModal = document.createElement('div');
    quickViewModal.className = 'modal';
    quickViewModal.style.display = 'block';
    
    quickViewModal.innerHTML = `
        <div class="modal-content" style="max-width: 80%; max-height: 90vh; overflow-y: auto;">
            <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
            <div style="display: flex; gap: 2rem; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 300px;">
                    <img src="${dress.image}" alt="${dress.name}" style="width: 100%; border-radius: 10px;">
                </div>
                <div style="flex: 1; min-width: 300px;">
                    <h2>${dress.name}</h2>
                    <div class="dress-rating" style="margin: 1rem 0;">
                        <div class="stars">${generateStars(dress.rating)}</div>
                        <span class="rating-text">${dress.rating} (${dress.reviews} reviews)</span>
                    </div>
                    <div style="font-size: 1.5rem; font-weight: bold; color: #ff6b9d; margin: 1rem 0;">
                        ₹${dress.price}
                    </div>
                    <p style="color: #7f8c8d; margin: 1rem 0;">${dress.description}</p>
                    
                    <div style="margin: 1.5rem 0;">
                        <h4>Available Fabrics:</h4>
                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin-top: 0.5rem;">
                            ${dress.fabric.map(f => `<span class="feature-tag">${f}</span>`).join('')}
                        </div>
                    </div>
                    
                    <div style="margin: 1.5rem 0;">
                        <h4>Available Colors:</h4>
                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin-top: 0.5rem;">
                            ${dress.colors.map(c => `<span class="feature-tag">${c}</span>`).join('')}
                        </div>
                    </div>
                    
                    <div style="margin: 1.5rem 0;">
                        <h4>Delivery Times:</h4>
                        <ul style="margin-top: 0.5rem;">
                            <li>Express: ${dress.deliveryTime.express}</li>
                            <li>Standard: ${dress.deliveryTime.standard}</li>
                            <li>Regular: ${dress.deliveryTime.regular}</li>
                        </ul>
                    </div>
                    
                    <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                        <button class="cta-button" onclick="openCustomization(${dress.id})">
                            <i class="fas fa-magic"></i> Customize & Order
                        </button>
                        <button class="btn-secondary" onclick="toggleWishlist(${dress.id})">
                            <i class="${wishlist.includes(dress.id) ? 'fas' : 'far'} fa-heart"></i> Wishlist
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(quickViewModal);
}

// Setup event listeners
function setupEventListeners() {
    // Navlinks
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            scrollToSection(targetId);
        });
    });
    
    // Close modal when clicking outside
    window.onclick = function(event) {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    }
    
    // Contact form
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            showMessage('Thank you for your message! We will get back to you soon.', 'success');
            this.reset();
        });
    }
}

// Scroll to section
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        const sectionTop = section.offsetTop - 80;
        
        window.scrollTo({
            top: sectionTop,
            behavior: 'smooth'
        });
    }
}

// Modal functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

function switchModal(currentModalId, newModalId) {
    closeModal(currentModalId);
    openModal(newModalId);
}

// Show message
function showMessage(text, type = 'info') {
    const message = document.createElement('div');
    message.className = `message ${type}`;
    message.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${text}
    `;
    
    message.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(message);
    
    setTimeout(() => {
        message.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            message.remove();
        }, 300);
    }, 3000);
}

// Check login state on page load
function checkLoginState() {
    const token = localStorage.getItem('token');
    if (token) {
        updateUIForAuth();
    }
}

// Update UI based on authentication state
function updateUIForAuth() {
    const userData = JSON.parse(localStorage.getItem('userData'));
    const userMenu = document.getElementById('user-menu');
    const authMenu = document.getElementById('auth-menu');
    const mobileUserMenu = document.getElementById('mobile-user-menu');
    const mobileAuthMenu = document.getElementById('mobile-auth-menu');
    
    if (userData) {
        // User is logged in
        if (userMenu) userMenu.style.display = 'block';
        if (authMenu) authMenu.style.display = 'none';
        if (mobileUserMenu) mobileUserMenu.style.display = 'block';
        if (mobileAuthMenu) mobileAuthMenu.style.display = 'none';
    } else {
        // User is not logged in
        if (userMenu) userMenu.style.display = 'none';
        if (authMenu) authMenu.style.display = 'block';
        if (mobileUserMenu) mobileUserMenu.style.display = 'none';
        if (mobileAuthMenu) mobileAuthMenu.style.display = 'block';
    }
}

// Open customization modal
function openCustomization(dressId) {
    const dress = dressesData.find(d => d.id === dressId);
    if (!dress) return;
    
    // Check if user is logged in
    const token = localStorage.getItem('token');
    if (!token) {
        showMessage('Please login to customize and order', 'info');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
        return;
    }
    
    // Store dress data for customization
    localStorage.setItem('selectedDress', JSON.stringify(dress));
    window.location.href = 'customize.html';
}

// Add CSS animations
function addAnimations() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        
        .list-view {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .list-view .dress-card {
            display: flex;
            max-width: none;
            height: 200px;
        }
        
        .list-view .dress-image {
            width: 200px;
            height: 200px;
            flex-shrink: 0;
        }
        
        .list-view .dress-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 1rem;
        }
        
        .wishlisted {
            color: #e74c3c !important;
            background: rgba(231, 76, 60, 0.1) !important;
        }
        
        .btn-secondary {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-1px);
        }
    `;
    document.head.appendChild(style);
}

// Smooth scrolling for navigation
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add scroll effect
window.addEventListener('scroll', function() {
    const header = document.querySelector('.header');
    if (header) {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 107, 157, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
        } else {
            header.style.background = 'linear-gradient(135deg, #ff6b9d, #c44569)';
            header.style.backdropFilter = 'none';
        }
    }
});

// Initialize animations
addAnimations();