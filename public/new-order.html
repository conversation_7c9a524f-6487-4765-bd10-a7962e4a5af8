<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Custom Dress - Tailored Perfection</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .order-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .order-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .progress-bar {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
            position: relative;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 1;
            flex: 1;
            max-width: 200px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .step-number.active {
            background: #667eea;
            color: white;
        }

        .step-number.completed {
            background: #4caf50;
            color: white;
        }

        .step-label {
            font-size: 14px;
            color: #666;
            text-align: center;
        }

        .step-label.active {
            color: #667eea;
            font-weight: bold;
        }

        .form-step {
            display: none;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .form-step.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group.error input,
        .form-group.error select {
            border-color: #f44336;
        }

        .error-message {
            color: #f44336;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }

        .form-group.error .error-message {
            display: block;
        }

        .btn-group {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .price-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .price-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .price-total {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
            border-top: 2px solid #667eea;
            padding-top: 10px;
            margin-top: 10px;
        }

        .saved-profiles {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .profile-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .profile-item:hover {
            background: #f5f5f5;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .order-container {
                padding: 10px;
            }
            
            .form-step {
                padding: 20px;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
                gap: 10px;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="order-container">
        <div class="order-header">
            <h1>Order Your Custom Dress</h1>
            <p>Create your perfect dress with our expert tailoring service</p>
        </div>

        <div class="progress-bar">
            <div class="progress-step">
                <div class="step-number active" id="step1">1</div>
                <div class="step-label active">Style Selection</div>
            </div>
            <div class="progress-step">
                <div class="step-number" id="step2">2</div>
                <div class="step-label">Measurements</div>
            </div>
            <div class="progress-step">
                <div class="step-number" id="step3">3</div>
                <div class="step-label">Delivery Details</div>
            </div>
            <div class="progress-step">
                <div class="step-number" id="step4">4</div>
                <div class="step-label">Review & Order</div>
            </div>
        </div>

        <!-- Step 1: Style Selection -->
        <div class="form-step active" id="step1-content">
            <h2>Choose Your Style</h2>
            <div class="form-grid">
                <div class="form-group">
                    <label for="style">Dress Style *</label>
                    <select id="style" required>
                        <option value="">Select a style</option>
                        <option value="A-line">A-line Dress</option>
                        <option value="Sheath">Sheath Dress</option>
                        <option value="Wrap">Wrap Dress</option>
                        <option value="Maxi">Maxi Dress</option>
                        <option value="Midi">Midi Dress</option>
                        <option value="Mini">Mini Dress</option>
                    </select>
                    <div class="error-message">Please select a style</div>
                </div>

                <div class="form-group">
                    <label for="fabric">Fabric *</label>
                    <select id="fabric" required>
                        <option value="">Select fabric</option>
                        <option value="Cotton">Cotton - $50</option>
                        <option value="Silk">Silk - $120</option>
                        <option value="Linen">Linen - $80</option>
                        <option value="Wool">Wool - $100</option>
                        <option value="Polyester">Polyester - $40</option>
                    </select>
                    <div class="error-message">Please select a fabric</div>
                </div>

                <div class="form-group">
                    <label for="color">Color *</label>
                    <input type="color" id="color" value="#000000" required>
                    <div class="error-message">Please select a color</div>
                </div>

                <div class="form-group">
                    <label for="size">Base Size *</label>
                    <select id="size" required>
                        <option value="">Select size</option>
                        <option value="XS">XS (Extra Small)</option>
                        <option value="S">S (Small)</option>
                        <option value="M">M (Medium)</option>
                        <option value="L">L (Large)</option>
                        <option value="XL">XL (Extra Large)</option>
                        <option value="XXL">XXL (2X Large)</option>
                    </select>
                    <div class="error-message">Please select a size</div>
                </div>
            </div>

            <div class="btn-group">
                <button type="button" class="btn btn-primary" onclick="nextStep()">Next: Measurements</button>
            </div>
        </div>

        <!-- Step 2: Measurements -->
        <div class="form-step" id="step2-content">
            <h2>Enter Your Measurements</h2>
            
            <div id="savedMeasurements" class="saved-profiles" style="display: none;">
                <h4>Saved Measurements</h4>
                <div id="measurementsList"></div>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label for="bust">Bust (inches) *</label>
                    <input type="number" id="bust" min="20" max="60" step="0.5" required>
                    <div class="error-message">Please enter a valid bust measurement</div>
                </div>

                <div class="form-group">
                    <label for="waist">Waist (inches) *</label>
                    <input type="number" id="waist" min="20" max="50" step="0.5" required>
                    <div class="error-message">Please enter a valid waist measurement</div>
                </div>

                <div class="form-group">
                    <label for="hips">Hips (inches) *</label>
                    <input type="number" id="hips" min="20" max="60" step="0.5" required>
                    <div class="error-message">Please enter a valid hips measurement</div>
                </div>

                <div class="form-group">
                    <label for="length">Dress Length (inches) *</label>
                    <input type="number" id="length" min="20" max="70" step="0.5" required>
                    <div class="error-message">Please enter a valid length</div>
                </div>
            </div>

            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="previousStep()">Back</button>
                <button type="button" class="btn btn-primary" onclick="nextStep()">Next: Delivery</button>
            </div>
        </div>

        <!-- Step 3: Delivery Details -->
        <div class="form-step" id="step3-content">
            <h2>Delivery Information</h2>
            
            <div id="savedAddresses" class="saved-profiles" style="display: none;">
                <h4>Saved Addresses</h4>
                <div id="addressesList"></div>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label for="fullName">Full Name *</label>
                    <input type="text" id="fullName" required>
                    <div class="error-message">Please enter your full name</div>
                </div>

                <div class="form-group">
                    <label for="phone">Phone Number *</label>
                    <input type="tel" id="phone" required>
                    <div class="error-message">Please enter a valid phone number</div>
                </div>

                <div class="form-group">
                    <label for="email">Email *</label>
                    <input type="email" id="email" required>
                    <div class="error-message">Please enter a valid email</div>
                </div>

                <div class="form-group">
                    <label for="address">Street Address *</label>
                    <input type="text" id="address" required>
                    <div class="error-message">Please enter your address</div>
                </div>

                <div class="form-group">
                    <label for="city">City *</label>
                    <input type="text" id="city" required>
                    <div class="error-message">Please enter your city</div>
                </div>

                <div class="form-group">
                    <label for="state">State/Province *</label>
                    <input type="text" id="state" required>
                    <div class="error-message">Please enter your state</div>
                </div>

                <div class="form-group">
                    <label for="zipCode">ZIP/Postal Code *</label>
                    <input type="text" id="zipCode" required>
                    <div class="error-message">Please enter your ZIP code</div>
                </div>

                <div class="form-group">
                    <label for="country">Country *</label>
                    <input type="text" id="country" value="United States" required>
                    <div class="error-message">Please enter your country</div>
                </div>
            </div>

            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="previousStep()">Back</button>
                <button type="button" class="btn btn-primary" onclick="nextStep()">Next: Review</button>
            </div>
        </div>

        <!-- Step 4: Review & Order -->
        <div class="form-step" id="step4-content">
            <h2>Review Your Order</h2>
            
            <div class="price-summary">
                <h3>Order Summary</h3>
                <div class="price-item">
                    <span>Style:</span>
                    <span id="reviewStyle"></span>
                </div>
                <div class="price-item">
                    <span>Fabric:</span>
                    <span id="reviewFabric"></span>
                </div>
                <div class="price-item">
                    <span>Color:</span>
                    <span id="reviewColor"></span>
                </div>
                <div class="price-item">
                    <span>Size:</span>
                    <span id="reviewSize"></span>
                </div>
                <div class="price-item">
                    <span>Base Price:</span>
                    <span id="basePrice">$0.00</span>
                </div>
                <div class="price-item">
                    <span>Customization:</span>
                    <span id="customPrice">$25.00</span>
                </div>
                <div class="price-total">
                    <span>Total:</span>
                    <span id="totalPrice">$0.00</span>
                </div>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="terms" required>
                    I agree to the terms and conditions
                </label>
                <div class="error-message">Please accept the terms and conditions</div>
            </div>

            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="previousStep()">Back</button>
                <button type="button" class="btn btn-primary" onclick="submitOrder()" id="submitBtn">
                    <span id="submitText">Place Order</span>
                    <div class="loading" id="loadingSpinner">
                        <div class="spinner"></div>
                        Processing...
                    </div>
                </button>
            </div>
        </div>
    </div>

    <script src="auth.js"></script>
    <script>
        let currentStep = 1;
        let userProfile = null;

        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
            loadUserProfile();
            setupEventListeners();
        });

        async function checkAuthentication() {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = `login.html?redirect=${encodeURIComponent(window.location.pathname + window.location.search)}`;
                return;
            }
        }

        async function loadUserProfile() {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/auth/users/me/profile', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    userProfile = await response.json();
                    displaySavedProfiles();
                }
            } catch (error) {
                console.error('Failed to load user profile:', error);
            }
        }

        function displaySavedProfiles() {
            if (userProfile?.measurements?.length > 0) {
                document.getElementById('savedMeasurements').style.display = 'block';
                const list = document.getElementById('measurementsList');
                list.innerHTML = '';
                
                userProfile.measurements.forEach((measurement, index) => {
                    const item = document.createElement('div');
                    item.className = 'profile-item';
                    item.innerHTML = `
                        <span>Bust: ${measurement.bust}", Waist: ${measurement.waist}", Hips: ${measurement.hips}"</span>
                        <button type="button" class="btn btn-primary" onclick="useMeasurement(${index})">Use</button>
                    `;
                    list.appendChild(item);
                });
            }

            if (userProfile?.addresses?.length > 0) {
                document.getElementById('savedAddresses').style.display = 'block';
                const list = document.getElementById('addressesList');
                list.innerHTML = '';
                
                userProfile.addresses.forEach((address, index) => {
                    const item = document.createElement('div');
                    item.className = 'profile-item';
                    item.innerHTML = `
                        <span>${address.fullName}, ${address.address}, ${address.city}</span>
                        <button type="button" class="btn btn-primary" onclick="useAddress(${index})">Use</button>
                    `;
                    list.appendChild(item);
                });
            }
        }

        function useMeasurement(index) {
            const measurement = userProfile.measurements[index];
            document.getElementById('bust').value = measurement.bust;
            document.getElementById('waist').value = measurement.waist;
            document.getElementById('hips').value = measurement.hips;
            document.getElementById('length').value = measurement.length;
        }

        function useAddress(index) {
            const address = userProfile.addresses[index];
            document.getElementById('fullName').value = address.fullName;
            document.getElementById('phone').value = address.phone;
            document.getElementById('email').value = address.email;
            document.getElementById('address').value = address.address;
            document.getElementById('city').value = address.city;
            document.getElementById('state').value = address.state;
            document.getElementById('zipCode').value = address.zipCode;
            document.getElementById('country').value = address.country;
        }

        function setupEventListeners() {
            // Update price when selections change
            ['style', 'fabric', 'color', 'size'].forEach(id => {
                document.getElementById(id).addEventListener('change', updatePrice);
            });
        }

        function updatePrice() {
            const fabric = document.getElementById('fabric').value;
            const basePrices = {
                'Cotton': 50,
                'Silk': 120,
                'Linen': 80,
                'Wool': 100,
                'Polyester': 40
            };
            
            const basePrice = basePrices[fabric] || 0;
            const customPrice = 25;
            const totalPrice = basePrice + customPrice;
            
            document.getElementById('basePrice').textContent = `$${basePrice.toFixed(2)}`;
            document.getElementById('customPrice').textContent = `$${customPrice.toFixed(2)}`;
            document.getElementById('totalPrice').textContent = `$${totalPrice.toFixed(2)}`;
        }

        function nextStep() {
            if (validateStep(currentStep)) {
                document.getElementById(`step${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.add('completed');
                document.getElementById(`step${currentStep}-content`).classList.remove('active');
                
                currentStep++;
                
