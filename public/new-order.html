<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Custom Dress - <PERSON><PERSON>'s Couture</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .order-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .order-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .progress-bar {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
            position: relative;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 1;
            flex: 1;
            max-width: 200px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .step-number.active {
            background: #667eea;
            color: white;
        }

        .step-number.completed {
            background: #4caf50;
            color: white;
        }

        .step-label {
            font-size: 14px;
            color: #666;
            text-align: center;
        }

        .step-label.active {
            color: #667eea;
            font-weight: bold;
        }

        .form-step {
            display: none;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .form-step.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group.error input,
        .form-group.error select {
            border-color: #f44336;
        }

        .error-message {
            color: #f44336;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }

        .form-group.error .error-message {
            display: block;
        }

        .btn-group {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .price-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .price-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .price-total {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
            border-top: 2px solid #667eea;
            padding-top: 10px;
            margin-top: 10px;
        }

        .saved-profiles {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .profile-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .profile-item:hover {
            background: #f5f5f5;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .order-container {
                padding: 10px;
            }
            
            .form-step {
                padding: 20px;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
                gap: 10px;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="order-container">
        <header class="order-header">
            <h1>Create Your Perfect Dress</h1>
            <p>Experience the art of custom tailoring with Nishi's Couture. Follow our simple 4-step process to create a dress that's uniquely yours.</p>

            <!-- Enhanced Progress Indicator -->
            <div class="progress-container" role="progressbar" aria-valuenow="1" aria-valuemin="1" aria-valuemax="4">
                <div class="progress-bar">
                    <div class="progress-step active" data-step="1">
                        <div class="step-circle">
                            <span class="step-number">1</span>
                            <i class="fas fa-check step-check" aria-hidden="true"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-label">Style Selection</div>
                            <div class="step-description">Choose your design</div>
                        </div>
                    </div>

                    <div class="progress-connector"></div>

                    <div class="progress-step" data-step="2">
                        <div class="step-circle">
                            <span class="step-number">2</span>
                            <i class="fas fa-check step-check" aria-hidden="true"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-label">Measurements</div>
                            <div class="step-description">Perfect fit details</div>
                        </div>
                    </div>

                    <div class="progress-connector"></div>

                    <div class="progress-step" data-step="3">
                        <div class="step-circle">
                            <span class="step-number">3</span>
                            <i class="fas fa-check step-check" aria-hidden="true"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-label">Delivery</div>
                            <div class="step-description">Address & timing</div>
                        </div>
                    </div>

                    <div class="progress-connector"></div>

                    <div class="progress-step" data-step="4">
                        <div class="step-circle">
                            <span class="step-number">4</span>
                            <i class="fas fa-check step-check" aria-hidden="true"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-label">Review & Order</div>
                            <div class="step-description">Confirm & pay</div>
                        </div>
                    </div>
                </div>

                <div class="progress-line">
                    <div class="progress-fill" style="width: 25%"></div>
                </div>
            </div>
        </header>

        <!-- Step 1: Style Selection -->
        <div class="form-step active" id="step1-content">
            <div class="step-header">
                <h2>
                    <i class="fas fa-palette" aria-hidden="true"></i>
                    Choose Your Perfect Style
                </h2>
                <p class="step-description">Select your preferred dress style, fabric, color, and base size. We'll customize everything to your exact measurements in the next steps.</p>
            </div>

            <div class="selection-container">
                <!-- Style Selection -->
                <div class="selection-group">
                    <h3>
                        <i class="fas fa-dress" aria-hidden="true"></i>
                        Dress Style
                    </h3>
                    <div class="style-grid" role="radiogroup" aria-label="Dress style selection">
                        <label class="style-card" for="style-aline">
                            <input type="radio" id="style-aline" name="style" value="A-line" required>
                            <div class="card-content">
                                <div class="style-icon">
                                    <i class="fas fa-female" aria-hidden="true"></i>
                                </div>
                                <h4>A-line Dress</h4>
                                <p>Classic silhouette that flatters all body types</p>
                                <span class="price-tag">From ₹2,500</span>
                            </div>
                        </label>

                        <label class="style-card" for="style-sheath">
                            <input type="radio" id="style-sheath" name="style" value="Sheath" required>
                            <div class="card-content">
                                <div class="style-icon">
                                    <i class="fas fa-user-tie" aria-hidden="true"></i>
                                </div>
                                <h4>Sheath Dress</h4>
                                <p>Sleek and sophisticated for professional wear</p>
                                <span class="price-tag">From ₹3,000</span>
                            </div>
                        </label>

                        <label class="style-card" for="style-wrap">
                            <input type="radio" id="style-wrap" name="style" value="Wrap" required>
                            <div class="card-content">
                                <div class="style-icon">
                                    <i class="fas fa-heart" aria-hidden="true"></i>
                                </div>
                                <h4>Wrap Dress</h4>
                                <p>Comfortable and versatile for any occasion</p>
                                <span class="price-tag">From ₹2,800</span>
                            </div>
                        </label>

                        <label class="style-card" for="style-maxi">
                            <input type="radio" id="style-maxi" name="style" value="Maxi" required>
                            <div class="card-content">
                                <div class="style-icon">
                                    <i class="fas fa-crown" aria-hidden="true"></i>
                                </div>
                                <h4>Maxi Dress</h4>
                                <p>Elegant floor-length for special occasions</p>
                                <span class="price-tag">From ₹4,000</span>
                            </div>
                        </label>

                        <label class="style-card" for="style-midi">
                            <input type="radio" id="style-midi" name="style" value="Midi" required>
                            <div class="card-content">
                                <div class="style-icon">
                                    <i class="fas fa-star" aria-hidden="true"></i>
                                </div>
                                <h4>Midi Dress</h4>
                                <p>Perfect balance of elegance and comfort</p>
                                <span class="price-tag">From ₹3,200</span>
                            </div>
                        </label>

                        <label class="style-card" for="style-mini">
                            <input type="radio" id="style-mini" name="style" value="Mini" required>
                            <div class="card-content">
                                <div class="style-icon">
                                    <i class="fas fa-bolt" aria-hidden="true"></i>
                                </div>
                                <h4>Mini Dress</h4>
                                <p>Trendy and youthful for casual events</p>
                                <span class="price-tag">From ₹2,200</span>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Fabric Selection -->
                <div class="selection-group">
                    <h3>
                        <i class="fas fa-cut" aria-hidden="true"></i>
                        Premium Fabrics
                    </h3>
                    <div class="fabric-grid" role="radiogroup" aria-label="Fabric selection">
                        <label class="fabric-card" for="fabric-cotton">
                            <input type="radio" id="fabric-cotton" name="fabric" value="Cotton" required>
                            <div class="card-content">
                                <div class="fabric-swatch cotton"></div>
                                <h4>Premium Cotton</h4>
                                <p>Breathable and comfortable for daily wear</p>
                                <span class="price-tag">₹1,500</span>
                            </div>
                        </label>

                        <label class="fabric-card" for="fabric-silk">
                            <input type="radio" id="fabric-silk" name="fabric" value="Silk" required>
                            <div class="card-content">
                                <div class="fabric-swatch silk"></div>
                                <h4>Pure Silk</h4>
                                <p>Luxurious and elegant for special occasions</p>
                                <span class="price-tag">₹3,500</span>
                            </div>
                        </label>

                        <label class="fabric-card" for="fabric-linen">
                            <input type="radio" id="fabric-linen" name="fabric" value="Linen" required>
                            <div class="card-content">
                                <div class="fabric-swatch linen"></div>
                                <h4>Fine Linen</h4>
                                <p>Natural and airy for summer comfort</p>
                                <span class="price-tag">₹2,200</span>
                            </div>
                        </label>

                        <label class="fabric-card" for="fabric-wool">
                            <input type="radio" id="fabric-wool" name="fabric" value="Wool" required>
                            <div class="card-content">
                                <div class="fabric-swatch wool"></div>
                                <h4>Merino Wool</h4>
                                <p>Warm and sophisticated for cooler weather</p>
                                <span class="price-tag">₹2,800</span>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Color & Size Row -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="color">
                            <i class="fas fa-palette" aria-hidden="true"></i>
                            Choose Color
                        </label>
                        <div class="color-picker-wrapper">
                            <input type="color" id="color" value="#000000" required>
                            <div class="color-preview" id="color-preview"></div>
                            <span class="color-name" id="color-name">Black</span>
                        </div>
                        <div class="error-message">Please select a color</div>
                    </div>

                    <div class="form-group">
                        <label for="size">
                            <i class="fas fa-ruler-combined" aria-hidden="true"></i>
                            Base Size
                        </label>
                        <select id="size" required class="enhanced-select">
                            <option value="">Select your base size</option>
                            <option value="XS">XS (Extra Small) - 30-32"</option>
                            <option value="S">S (Small) - 32-34"</option>
                            <option value="M">M (Medium) - 34-36"</option>
                            <option value="L">L (Large) - 36-38"</option>
                            <option value="XL">XL (Extra Large) - 38-40"</option>
                            <option value="XXL">XXL (2X Large) - 40-42"</option>
                        </select>
                        <div class="size-note">We'll customize the exact fit with your measurements</div>
                        <div class="error-message">Please select a size</div>
                    </div>
                </div>
            </div>

            <div class="step-summary" id="step1-summary">
                <h4>Your Selection Summary</h4>
                <div class="summary-content">
                    <p>Please make your selections above to see the summary</p>
                </div>
            </div>

            <div class="step-navigation">
                <button type="button" class="btn btn-secondary" disabled>
                    <i class="fas fa-arrow-left" aria-hidden="true"></i>
                    Previous
                </button>
                <button type="button" class="btn btn-primary" onclick="nextStep()" disabled id="step1-next">
                    Next: Measurements
                    <i class="fas fa-arrow-right" aria-hidden="true"></i>
                </button>
            </div>
        </div>

        <!-- Step 2: Measurements -->
        <div class="form-step" id="step2-content">
            <h2>Enter Your Measurements</h2>
            
            <div id="savedMeasurements" class="saved-profiles" style="display: none;">
                <h4>Saved Measurements</h4>
                <div id="measurementsList"></div>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label for="bust">Bust (inches) *</label>
                    <input type="number" id="bust" min="20" max="60" step="0.5" required>
                    <div class="error-message">Please enter a valid bust measurement</div>
                </div>

                <div class="form-group">
                    <label for="waist">Waist (inches) *</label>
                    <input type="number" id="waist" min="20" max="50" step="0.5" required>
                    <div class="error-message">Please enter a valid waist measurement</div>
                </div>

                <div class="form-group">
                    <label for="hips">Hips (inches) *</label>
                    <input type="number" id="hips" min="20" max="60" step="0.5" required>
                    <div class="error-message">Please enter a valid hips measurement</div>
                </div>

                <div class="form-group">
                    <label for="length">Dress Length (inches) *</label>
                    <input type="number" id="length" min="20" max="70" step="0.5" required>
                    <div class="error-message">Please enter a valid length</div>
                </div>
            </div>

            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="previousStep()">Back</button>
                <button type="button" class="btn btn-primary" onclick="nextStep()">Next: Delivery</button>
            </div>
        </div>

        <!-- Step 3: Delivery Details -->
        <div class="form-step" id="step3-content">
            <h2>Delivery Information</h2>
            
            <div id="savedAddresses" class="saved-profiles" style="display: none;">
                <h4>Saved Addresses</h4>
                <div id="addressesList"></div>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label for="fullName">Full Name *</label>
                    <input type="text" id="fullName" required>
                    <div class="error-message">Please enter your full name</div>
                </div>

                <div class="form-group">
                    <label for="phone">Phone Number *</label>
                    <input type="tel" id="phone" required>
                    <div class="error-message">Please enter a valid phone number</div>
                </div>

                <div class="form-group">
                    <label for="email">Email *</label>
                    <input type="email" id="email" required>
                    <div class="error-message">Please enter a valid email</div>
                </div>

                <div class="form-group">
                    <label for="address">Street Address *</label>
                    <input type="text" id="address" required>
                    <div class="error-message">Please enter your address</div>
                </div>

                <div class="form-group">
                    <label for="city">City *</label>
                    <input type="text" id="city" required>
                    <div class="error-message">Please enter your city</div>
                </div>

                <div class="form-group">
                    <label for="state">State/Province *</label>
                    <input type="text" id="state" required>
                    <div class="error-message">Please enter your state</div>
                </div>

                <div class="form-group">
                    <label for="zipCode">ZIP/Postal Code *</label>
                    <input type="text" id="zipCode" required>
                    <div class="error-message">Please enter your ZIP code</div>
                </div>

                <div class="form-group">
                    <label for="country">Country *</label>
                    <input type="text" id="country" value="United States" required>
                    <div class="error-message">Please enter your country</div>
                </div>
            </div>

            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="previousStep()">Back</button>
                <button type="button" class="btn btn-primary" onclick="nextStep()">Next: Review</button>
            </div>
        </div>

        <!-- Step 4: Review & Order -->
        <div class="form-step" id="step4-content">
            <h2>Review Your Order</h2>
            
            <div class="price-summary">
                <h3>Order Summary</h3>
                <div class="price-item">
                    <span>Style:</span>
                    <span id="reviewStyle"></span>
                </div>
                <div class="price-item">
                    <span>Fabric:</span>
                    <span id="reviewFabric"></span>
                </div>
                <div class="price-item">
                    <span>Color:</span>
                    <span id="reviewColor"></span>
                </div>
                <div class="price-item">
                    <span>Size:</span>
                    <span id="reviewSize"></span>
                </div>
                <div class="price-item">
                    <span>Base Price:</span>
                    <span id="basePrice">$0.00</span>
                </div>
                <div class="price-item">
                    <span>Customization:</span>
                    <span id="customPrice">$25.00</span>
                </div>
                <div class="price-total">
                    <span>Total:</span>
                    <span id="totalPrice">$0.00</span>
                </div>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="terms" required>
                    I agree to the terms and conditions
                </label>
                <div class="error-message">Please accept the terms and conditions</div>
            </div>

            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="previousStep()">Back</button>
                <button type="button" class="btn btn-primary" onclick="submitOrder()" id="submitBtn">
                    <span id="submitText">Place Order</span>
                    <div class="loading" id="loadingSpinner">
                        <div class="spinner"></div>
                        Processing...
                    </div>
                </button>
            </div>
        </div>
    </div>

    <script src="auth.js"></script>
    <script>
        let currentStep = 1;
        let userProfile = null;

        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
            loadUserProfile();
            setupEventListeners();
        });

        async function checkAuthentication() {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = `login.html?redirect=${encodeURIComponent(window.location.pathname + window.location.search)}`;
                return;
            }
        }

        async function loadUserProfile() {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/auth/users/me/profile', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    userProfile = await response.json();
                    displaySavedProfiles();
                }
            } catch (error) {
                console.error('Failed to load user profile:', error);
            }
        }

        function displaySavedProfiles() {
            if (userProfile?.measurements?.length > 0) {
                document.getElementById('savedMeasurements').style.display = 'block';
                const list = document.getElementById('measurementsList');
                list.innerHTML = '';
                
                userProfile.measurements.forEach((measurement, index) => {
                    const item = document.createElement('div');
                    item.className = 'profile-item';
                    item.innerHTML = `
                        <span>Bust: ${measurement.bust}", Waist: ${measurement.waist}", Hips: ${measurement.hips}"</span>
                        <button type="button" class="btn btn-primary" onclick="useMeasurement(${index})">Use</button>
                    `;
                    list.appendChild(item);
                });
            }

            if (userProfile?.addresses?.length > 0) {
                document.getElementById('savedAddresses').style.display = 'block';
                const list = document.getElementById('addressesList');
                list.innerHTML = '';
                
                userProfile.addresses.forEach((address, index) => {
                    const item = document.createElement('div');
                    item.className = 'profile-item';
                    item.innerHTML = `
                        <span>${address.fullName}, ${address.address}, ${address.city}</span>
                        <button type="button" class="btn btn-primary" onclick="useAddress(${index})">Use</button>
                    `;
                    list.appendChild(item);
                });
            }
        }

        function useMeasurement(index) {
            const measurement = userProfile.measurements[index];
            document.getElementById('bust').value = measurement.bust;
            document.getElementById('waist').value = measurement.waist;
            document.getElementById('hips').value = measurement.hips;
            document.getElementById('length').value = measurement.length;
        }

        function useAddress(index) {
            const address = userProfile.addresses[index];
            document.getElementById('fullName').value = address.fullName;
            document.getElementById('phone').value = address.phone;
            document.getElementById('email').value = address.email;
            document.getElementById('address').value = address.address;
            document.getElementById('city').value = address.city;
            document.getElementById('state').value = address.state;
            document.getElementById('zipCode').value = address.zipCode;
            document.getElementById('country').value = address.country;
        }

        function setupEventListeners() {
            // Update price when selections change
            ['style', 'fabric', 'color', 'size'].forEach(id => {
                document.getElementById(id).addEventListener('change', updatePrice);
            });
        }

        function updatePrice() {
            const fabric = document.getElementById('fabric').value;
            const basePrices = {
                'Cotton': 50,
                'Silk': 120,
                'Linen': 80,
                'Wool': 100,
                'Polyester': 40
            };
            
            const basePrice = basePrices[fabric] || 0;
            const customPrice = 25;
            const totalPrice = basePrice + customPrice;
            
            document.getElementById('basePrice').textContent = `$${basePrice.toFixed(2)}`;
            document.getElementById('customPrice').textContent = `$${customPrice.toFixed(2)}`;
            document.getElementById('totalPrice').textContent = `$${totalPrice.toFixed(2)}`;
        }

        function nextStep() {
            if (validateStep(currentStep)) {
                document.getElementById(`step${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.add('completed');
                document.getElementById(`step${currentStep}-content`).classList.remove('active');
                
                currentStep++;
                
