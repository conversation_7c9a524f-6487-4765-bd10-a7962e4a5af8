<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - Nishi's Couture</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .profile-header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .profile-welcome h1 {
            font-family: 'Playfair Display', serif;
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }

        .profile-welcome p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .profile-nav {
            display: flex;
            gap: 20px;
        }

        .profile-nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            background: rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .profile-nav a:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }

        .profile-container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
        }

        .profile-sidebar {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            height: fit-content;
            position: sticky;
            top: 20px;
        }

        .profile-avatar {
            text-align: center;
            margin-bottom: 30px;
        }

        .avatar-circle {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 2.5rem;
            color: white;
            font-weight: 600;
        }

        .profile-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .profile-email {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .profile-menu {
            list-style: none;
        }

        .profile-menu li {
            margin-bottom: 10px;
        }

        .profile-menu a {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            color: #2c3e50;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .profile-menu a:hover, .profile-menu a.active {
            background: #f8f9fa;
            color: #667eea;
        }

        .profile-menu i {
            width: 20px;
            text-align: center;
        }

        .profile-content {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header {
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .section-header h2 {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .section-header p {
            color: #7f8c8d;
            font-size: 1rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .form-group {
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group .input-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 1rem;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #ecf0f1;
            color: #2c3e50;
        }

        .btn-secondary:hover {
            background: #d5dbdb;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .address-card, .measurement-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
            transition: all 0.3s ease;
        }

        .address-card:hover, .measurement-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            font-weight: 600;
            color: #2c3e50;
        }

        .card-actions {
            display: flex;
            gap: 10px;
        }

        .card-actions button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: #7f8c8d;
            transition: color 0.3s ease;
        }

        .card-actions button:hover {
            color: #667eea;
        }

        .card-content {
            color: #7f8c8d;
            line-height: 1.6;
        }

        .default-badge {
            background: #27ae60;
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.7rem;
            margin-left: 10px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 1.3rem;
            margin-bottom: 10px;
        }

        .empty-state p {
            margin-bottom: 20px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .modal-header h3 {
            font-family: 'Playfair Display', serif;
            font-size: 1.5rem;
            color: #2c3e50;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #7f8c8d;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: #27ae60;
        }

        .notification.error {
            background: #e74c3c;
        }

        @media (max-width: 968px) {
            .profile-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .profile-sidebar {
                position: static;
            }

            .profile-header-content {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }

            .profile-nav {
                justify-content: center;
            }
        }

        @media (max-width: 600px) {
            .profile-content {
                padding: 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .btn-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header class="profile-header">
        <div class="profile-header-content">
            <div class="profile-welcome">
                <h1>Welcome back, <span id="userName">User</span></h1>
                <p>Manage your profile, addresses, and measurements</p>
            </div>
            <nav class="profile-nav">
                <a href="/" class="nav-link">
                    <i class="fas fa-home"></i> Home
                </a>
                <a href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </nav>
        </div>
    </header>

    <div class="profile-container">
        <aside class="profile-sidebar">
            <div class="profile-avatar">
                <div class="avatar-circle" id="avatarInitial">U</div>
                <div class="profile-name" id="sidebarName">User Name</div>
                <div class="profile-email" id="sidebarEmail"><EMAIL></div>
            </div>

            <nav>
                <ul class="profile-menu">
                    <li>
                        <a href="#" class="active" onclick="showSection('profile')">
                            <i class="fas fa-user"></i> Profile Information
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('addresses')">
                            <i class="fas fa-map-marker-alt"></i> My Addresses
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('measurements')">
                            <i class="fas fa-ruler"></i> My Measurements
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('orders')">
                            <i class="fas fa-shopping-bag"></i> Order History
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('security')">
                            <i class="fas fa-shield-alt"></i> Security
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <main class="profile-content">
            <!-- Profile Information Section -->
            <section id="profile-section" class="content-section active">
                <div class="section-header">
                    <h2>Profile Information</h2>
                    <p>Update your personal information and preferences</p>
                </div>

                <form id="profileForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="profileFullName">Full Name *</label>
                            <input type="text" id="profileFullName" name="fullName" required>
                            <i class="fas fa-user input-icon"></i>
                        </div>
                        <div class="form-group">
                            <label for="profileEmail">Email Address *</label>
                            <input type="email" id="profileEmail" name="email" required readonly>
                            <i class="fas fa-envelope input-icon"></i>
                        </div>
                        <div class="form-group">
                            <label for="profilePhone">Phone Number *</label>
                            <input type="tel" id="profilePhone" name="phoneNumber" required>
                            <i class="fas fa-phone input-icon"></i>
                        </div>
                        <div class="form-group">
                            <label for="profileDOB">Date of Birth</label>
                            <input type="date" id="profileDOB" name="dateOfBirth">
                            <i class="fas fa-calendar input-icon"></i>
                        </div>
                        <div class="form-group">
                            <label for="profileSize">Preferred Size Type</label>
                            <select id="profileSize" name="preferredSizeType">
                                <option value="">Select size type</option>
                                <option value="XS">XS - Extra Small</option>
                                <option value="S">S - Small</option>
                                <option value="M">M - Medium</option>
                                <option value="L">L - Large</option>
                                <option value="XL">XL - Extra Large</option>
                                <option value="XXL">XXL - Extra Extra Large</option>
                                <option value="Custom">Custom - I'll provide measurements</option>
                            </select>
                            <i class="fas fa-ruler input-icon"></i>
                        </div>
                    </div>

                    <div class="btn-group">
                        <button type="submit" class="btn btn-primary">Update Profile</button>
                        <button type="button" class="btn btn-secondary" onclick="resetProfileForm()">Reset</button>
                    </div>
                </form>
            </section>

            <!-- Addresses Section -->
            <section id="addresses-section" class="content-section">
                <div class="section-header">
                    <h2>My Addresses</h2>
                    <p>Manage your saved delivery addresses</p>
                </div>

                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="showAddAddressModal()">
                        <i class="fas fa-plus"></i> Add New Address
                    </button>
                </div>

                <div id="addressesList">
                    <div class="empty-state">
                        <i class="fas fa-map-marker-alt"></i>
                        <h3>No addresses saved</h3>
                        <p>Add your first address to make ordering faster</p>
                        <button class="btn btn-primary" onclick="showAddAddressModal()">Add Address</button>
                    </div>
                </div>
            </section>

            <!-- Measurements Section -->
            <section id="measurements-section" class="content-section">
                <div class="section-header">
                    <h2>My Measurements</h2>
                    <p>Save your measurements for quick ordering</p>
                </div>

                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="showAddMeasurementModal()">
                        <i class="fas fa-plus"></i> Add New Measurement
                    </button>
                </div>

                <div id="measurementsList">
                    <div class="empty-state">
                        <i class="fas fa-ruler"></i>
                        <h3>No measurements saved</h3>
                        <p>Add your measurements for perfect fitting clothes</p>
                        <button class="btn btn-primary" onclick="showAddMeasurementModal()">Add Measurement</button>
                    </div>
                </div>
            </section>

            <!-- Order History Section -->
            <section id="orders-section" class="content-section">
                <div class="section-header">
                    <h2>Order History</h2>
                    <p>View your past orders and their status</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalOrders">0</div>
                        <div class="stat-label">Total Orders</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="pendingOrders">0</div>
                        <div class="stat-label">Pending</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="completedOrders">0</div>
                        <div class="stat-label">Completed</div>
                    </div>
                </div>

                <div id="ordersList">
                    <div class="empty-state">
                        <i class="fas fa-shopping-bag"></i>
                        <h3>No orders yet</h3>
                        <p>Start shopping to see your order history</p>
                        <button class="btn btn-primary" onclick="window.location.href='/'">Browse Dresses</button>
                    </div>
                </div>
            </section>

            <!-- Security Section -->
            <section id="security-section" class="content-section">
                <div class="section-header">
                    <h2>Security Settings</h2>
                    <p>Manage your account security and password</p>
                </div>

                <form id="changePasswordForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="currentPassword">Current Password</label>
                            <input type="password" id="currentPassword" name="currentPassword" required>
                            <i class="fas fa-lock input-icon"></i>
                        </div>
                        <div class="form-group">
                            <label for="newPassword">New Password</label>
                            <input type="password" id="newPassword" name="newPassword" required>
                            <i class="fas fa-lock input-icon"></i>
                        </div>
                        <div class="form-group">
                            <label for="confirmNewPassword">Confirm New Password</label>
                            <input type="password" id="confirmNewPassword" name="confirmNewPassword" required>
                            <i class="fas fa-lock input-icon"></i>
                        </div>
                    </div>

                    <div class="btn-group">
                        <button type="submit" class="btn btn-primary">Change Password</button>
                    </div>
                </form>
            </section>
        </main>
    </div>

    <!-- Add Address Modal -->
    <div id="addressModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Address</h3>
                <button class="close-modal" onclick="closeAddressModal()">&times;</button>
            </div>
            <form id="addressForm">
                <div class="form-group">
                    <label for="addressLabel">Label (e.g., Home, Office)</label>
                    <input type="text" id="addressLabel" name="label" required placeholder="e.g., Home">
                </div>
                <div class="form-group">
                    <label for="streetAddress">Street Address *</label>
                    <input type="text" id="streetAddress" name="streetAddress" required placeholder="123 Main Street">
                </div>
                <div class="form-group">
                    <label for="city">City *</label>
                    <input type="text" id="city" name="city" required placeholder="Kochi">
                </div>
                <div class="form-group">
                    <label for="state">State *</label>
                    <input type="text" id="state" name="state" required placeholder="Kerala">
                </div>
                <div class="form-group">
                    <label for="zipCode">ZIP Code *</label>
                    <input type="text" id="zipCode" name="zipCode" required placeholder="682001">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="isDefaultAddress" name="isDefault">
                        Set as default address
                    </label>
                </div>
                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">Save Address</button>
                    <button type="button" class="btn btn-secondary" onclick="closeAddressModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Measurement Modal -->
    <div id="measurementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Measurement</h3>
                <button class="close-modal" onclick="closeMeasurementModal()">&times;</button>
            </div>
            <form id="measurementForm">
                <div class="form-group">
                    <label for="measurementLabel">Label (e.g., Standard, Party Wear)</label>
                    <input type="text" id="measurementLabel" name="label" required placeholder="e.g., Standard Size">
                </div>
                <div class="form-group">
                    <label for="bust">Bust (inches) *</label>
                    <input type="number" id="bust" name="bust" step="0.5" required placeholder="36">
                </div>
                <div class="form-group">
                    <label for="waist">Waist (inches) *</label>
                    <input type="number" id="waist" name="waist" step="0.5" required placeholder="28">
                </div>
                <div class="form-group">
                    <label for="hip">Hip (inches) *</label>
                    <input type="number" id="hip" name="hip" step="0.5" required placeholder="38">
                </div>
                <div class="form-group">
                    <label for="length">Length (inches) *</label>
                    <input type="number" id="length" name="length" step="0.5" required placeholder="60">
                </div>
                <div class="form-group">
                    <label for="unit">Unit</label>
                    <select id="unit" name="unit">
                        <option value="inches">Inches</option>
                        <option value="cm">Centimeters</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="isDefaultMeasurement" name="isDefault">
                        Set as default measurement
                    </label>
                </div>
                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">Save Measurement</button>
                    <button type="button" class="btn btn-secondary" onclick="closeMeasurementModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script src="profile.js"></script>
    <script>
        // Navigation and section management
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Remove active class from all menu items
            document.querySelectorAll('.profile-menu a').forEach(link => {
                link.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionName + '-section').classList.add('active');
            
            // Add active class to clicked menu item
            event.target.classList.add('active');
            
            // Load section data
            switch(sectionName) {
                case 'profile':
                    loadProfileData();
                    break;
                case 'addresses':
                    loadAddresses();
                    break;
                case 'measurements':
                    loadMeasurements();
                    break;
                case 'orders':
                    loadOrders();
                    break;
                case 'security':
                    // Security section doesn't need data loading
                    break;
            }
        }

        // Modal management
        function showAddAddressModal() {
            document.getElementById('addressModal').classList.add('active');
        }

        function closeAddressModal() {
            document.getElementById('addressModal').classList.remove('active');
            document.getElementById('addressForm').reset();
        }

        function showAddMeasurementModal() {
            document.getElementById('measurementModal').classList.add('active');
        }

        function closeMeasurementModal() {
            document.getElementById('measurementModal').classList.remove('active');
            document.getElementById('measurementForm').reset();
        }

        // Close modals when clicking outside
        window.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.classList.remove('active');
            }
        });

        // Notification system
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // Logout function
        function logout() {
            localStorage.removeItem('token');
            sessionStorage.removeItem('token');
            window.location.href = '/login.html';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadProfileData();
            loadAddresses();
            loadMeasurements();
            loadOrders();
        });
    </script>
</body>
</html>