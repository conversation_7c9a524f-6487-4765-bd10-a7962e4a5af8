<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - <PERSON><PERSON>'s Couture</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Reset Password</h1>
                <p>Create a new password for your account</p>
            </div>

            <form id="reset-password-form" class="auth-form">
                <div class="form-group">
                    <label for="password">New Password</label>
                    <input type="password" id="password" name="password" required minlength="6">
                </div>

                <div class="form-group">
                    <label for="confirmPassword">Confirm New Password</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required minlength="6">
                </div>

                <button type="submit" class="auth-btn">
                    <span class="btn-text">Reset Password</span>
                    <span class="btn-loading" style="display: none;">Resetting...</span>
                </button>
            </form>

            <div class="auth-footer">
                <p><a href="login.html">Back to login</a></p>
            </div>
        </div>
    </div>

    <script src="auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('reset-password-form');
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');

            if (!token) {
                showMessage('Invalid reset link', 'error');
                setTimeout(() => {
                    window.location.href = 'forgot-password.html';
                }, 2000);
                return;
            }

            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);
                
                // Validate passwords
                if (data.password !== data.confirmPassword) {
                    showMessage('Passwords do not match', 'error');
                    return;
                }
                
                try {
                    const response = await fetch('/api/auth/reset-password', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            token,
                            password: data.password
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        showMessage('Password reset successful! Redirecting to login...', 'success');
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 2000);
                    } else {
                        showMessage(result.error || 'Failed to reset password', 'error');
                    }
                } catch (error) {
                    showMessage('Network error. Please try again.', 'error');
                }
            });
        });
    </script>
</body>
</html>