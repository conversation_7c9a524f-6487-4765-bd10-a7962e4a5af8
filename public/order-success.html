<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmed - <PERSON><PERSON>'s Couture</title>
    <meta name="description" content="Your custom dress order has been confirmed. Thank you for choosing <PERSON><PERSON>'s Couture for your premium tailoring needs.">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .success-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 40px;
            text-align: center;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .success-icon {
            font-size: 80px;
            color: #4caf50;
            margin-bottom: 20px;
        }

        .success-title {
            color: #333;
            margin-bottom: 10px;
        }

        .success-message {
            color: #666;
            margin-bottom: 30px;
            font-size: 18px;
        }

        .order-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <i class="fas fa-check-circle success-icon"></i>
        <h1 class="success-title">Order Placed Successfully!</h1>
        <p class="success-message">Thank you for your order. We'll start working on your custom dress right away.</p>
        
        <div class="order-details" id="orderDetails">
            <h3>Order Details</h3>
            <div class="detail-item">
                <strong>Order ID:</strong>
                <span id="orderId">Loading...</span>
            </div>
            <div class="detail-item">
                <strong>Estimated Delivery:</strong>
                <span id="deliveryDate">Loading...</span>
            </div>
            <div class="detail-item">
                <strong>Total Amount:</strong>
                <span id="totalAmount">Loading...</span>
            </div>
        </div>

        <div>
            <a href="index.html" class="btn btn-primary">
                <i class="fas fa-home"></i> Back to Home
            </a>
            <a href="orders.html" class="btn btn-secondary">
                <i class="fas fa-list"></i> View My Orders
            </a>
        </div>
    </div>

    <script>
        // Load order details
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const orderId = urlParams.get('orderId');
            
            if (orderId) {
                loadOrderDetails(orderId);
            }
        });

        async function loadOrderDetails(orderId) {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch(`/api/orders/${orderId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const order = await response.json();
                    document.getElementById('orderId').textContent = order._id;
                    document.getElementById('deliveryDate').textContent = new Date(order.estimatedDelivery).toLocaleDateString();
                    document.getElementById('totalAmount').textContent = `$${order.price.toFixed(2)}`;
                }
            } catch (error) {
                console.error('Failed to load order details:', error);
            }
        }
    </script>
</body>
</html>