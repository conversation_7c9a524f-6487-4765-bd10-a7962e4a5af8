<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmed - <PERSON><PERSON>'s Couture</title>
    <meta name="description" content="Your custom dress order has been confirmed. Thank you for choosing <PERSON><PERSON>'s Couture for your premium tailoring needs.">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .success-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 40px;
            text-align: center;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .success-icon {
            font-size: 80px;
            color: #4caf50;
            margin-bottom: 20px;
        }

        .success-title {
            color: #333;
            margin-bottom: 10px;
        }

        .success-message {
            color: #666;
            margin-bottom: 30px;
            font-size: 18px;
        }

        .order-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <i class="fas fa-check-circle success-icon"></i>
        <h1 class="success-title">Order Placed Successfully!</h1>
        <p class="success-message">Thank you for your order. We'll start working on your custom dress right away.</p>
        
        <div class="order-details" id="orderDetails">
            <h3>Order Details</h3>
            <div class="detail-item">
                <strong>Order ID:</strong>
                <span id="orderId">Loading...</span>
            </div>
            <div class="detail-item">
                <strong>Estimated Delivery:</strong>
                <span id="deliveryDate">Loading...</span>
            </div>
            <div class="detail-item">
                <strong>Total Amount:</strong>
                <span id="totalAmount">Loading...</span>
            </div>
        </div>

        <div>
            <a href="index.html" class="btn btn-primary">
                <i class="fas fa-home"></i> Back to Home
            </a>
            <a href="orders.html" class="btn btn-secondary">
                <i class="fas fa-list"></i> View My Orders
            </a>
        </div>
    </div>

    <script>
        // Load order details from localStorage
        document.addEventListener('DOMContentLoaded', function() {
            loadOrderDetails();
        });

        function loadOrderDetails() {
            try {
                // Try to get order data from localStorage
                const orderData = localStorage.getItem('lastOrder') || localStorage.getItem('orderSuccess');

                if (orderData) {
                    const order = JSON.parse(orderData);
                    console.log('Loading order details:', order);
                    console.log('Order ID:', order.id || order.orderNumber);
                    console.log('Total Price:', order.pricing?.totalPrice || order.totalPrice);

                    // Update order ID
                    const orderIdElement = document.getElementById('orderId');
                    if (orderIdElement) {
                        orderIdElement.textContent = order.id || order.orderNumber || 'N/A';
                    }

                    // Update delivery date
                    const deliveryDateElement = document.getElementById('deliveryDate');
                    if (deliveryDateElement) {
                        if (order.delivery && order.delivery.estimatedDate) {
                            deliveryDateElement.textContent = order.delivery.estimatedDate;
                        } else if (order.estimatedDelivery) {
                            const deliveryDate = new Date(order.estimatedDelivery);
                            deliveryDateElement.textContent = deliveryDate.toLocaleDateString('en-IN', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                            });
                        } else {
                            deliveryDateElement.textContent = '7-10 business days';
                        }
                    }

                    // Update total amount
                    const totalAmountElement = document.getElementById('totalAmount');
                    if (totalAmountElement) {
                        const totalPrice = order.pricing?.totalPrice || order.totalPrice || 0;
                        totalAmountElement.textContent = `₹${totalPrice}`;
                    }

                    // Add additional order details if available
                    addAdditionalOrderDetails(order);

                } else {
                    console.log('No order data found in localStorage');
                    // Show default values
                    document.getElementById('orderId').textContent = 'Order not found';
                    document.getElementById('deliveryDate').textContent = 'N/A';
                    document.getElementById('totalAmount').textContent = 'N/A';
                }
            } catch (error) {
                console.error('Failed to load order details:', error);
                document.getElementById('orderId').textContent = 'Error loading order';
                document.getElementById('deliveryDate').textContent = 'N/A';
                document.getElementById('totalAmount').textContent = 'N/A';
            }
        }

        function addAdditionalOrderDetails(order) {
            const orderDetailsContainer = document.getElementById('orderDetails');

            // Add dress details
            if (order.dress && order.dress.name) {
                const dressDetail = document.createElement('div');
                dressDetail.className = 'detail-item';
                dressDetail.innerHTML = `
                    <strong>Dress:</strong>
                    <span>${order.dress.name}</span>
                `;
                orderDetailsContainer.appendChild(dressDetail);
            }

            // Add customization details
            if (order.customization) {
                const customDetail = document.createElement('div');
                customDetail.className = 'detail-item';
                customDetail.innerHTML = `
                    <strong>Customization:</strong>
                    <span>${order.customization.fabric || 'N/A'} fabric, ${order.customization.color || 'N/A'} color</span>
                `;
                orderDetailsContainer.appendChild(customDetail);
            }

            // Add tracking number if available
            if (order.trackingNumber) {
                const trackingDetail = document.createElement('div');
                trackingDetail.className = 'detail-item';
                trackingDetail.innerHTML = `
                    <strong>Tracking Number:</strong>
                    <span>${order.trackingNumber}</span>
                `;
                orderDetailsContainer.appendChild(trackingDetail);
            }
        }
    </script>
</body>
</html>