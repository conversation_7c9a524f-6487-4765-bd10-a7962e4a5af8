const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/kochi-boutique');

// Database Models (same as in server.js)
const DressSchema = new mongoose.Schema({
    name: { type: String, required: true },
    category: { type: String, required: true },
    basePrice: { type: Number, required: true },
    image: { type: String, required: true },
    features: [String],
    description: String,
    isActive: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now }
});

const OrderSchema = new mongoose.Schema({
    orderId: { type: String, unique: true, required: true },
    customerName: String,
    customerEmail: String,
    customerPhone: String,
    dressId: { type: mongoose.Schema.Types.ObjectId, ref: 'Dress' },
    measurements: {
        bust: Number,
        waist: Number,
        hip: Number,
        length: Number
    },
    customizations: {
        fabric: String,
        color: String,
        specialInstructions: String
    },
    deliveryOption: { type: String, enum: ['express', 'standard', 'regular'] },
    deliveryAddress: String,
    totalAmount: Number,
    status: { type: String, enum: ['pending', 'confirmed', 'in-progress', 'ready', 'dispatched', 'delivered'], default: 'pending' },
    porterOrderId: String,
    createdAt: { type: Date, default: Date.now }
});

const UserSchema = new mongoose.Schema({
    name: String,
    email: { type: String, unique: true },
    phone: String,
    password: String,
    role: { type: String, enum: ['customer', 'manager'], default: 'customer' },
    createdAt: { type: Date, default: Date.now }
});

const Dress = mongoose.model('Dress', DressSchema);
const Order = mongoose.model('Order', OrderSchema);
const User = mongoose.model('User', UserSchema);

// Sample data
const sampleDresses = [
    {
        name: "Elegant Saree Blouse",
        category: "blouse",
        basePrice: 1200,
        image: "https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=300",
        features: ["Custom Fit", "Same Day", "Premium Fabric"],
        description: "Beautifully crafted saree blouse with intricate embroidery work. Perfect for weddings and special occasions."
    },
    {
        name: "Designer Kurti",
        category: "kurti",
        basePrice: 800,
        image: "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=300",
        features: ["Cotton", "Comfortable", "Trendy"],
        description: "Comfortable cotton kurti perfect for daily wear. Available in multiple colors and patterns."
    },
    {
        name: "Party Wear Dress",
        category: "dress",
        basePrice: 2500,
        image: "https://images.unsplash.com/photo-1566479179817-c0b5b4b4b1e5?w=300",
        features: ["Evening Wear", "Luxury", "Custom Design"],
        description: "Stunning party dress with premium fabric and elegant design. Perfect for cocktail parties and events."
    },
    {
        name: "Traditional Saree",
        category: "saree",
        basePrice: 1800,
        image: "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=300",
        features: ["Handwoven", "Traditional", "Elegant"],
        description: "Authentic handwoven saree with traditional patterns. A timeless piece for cultural celebrations."
    },
    {
        name: "Casual Kurti",
        category: "kurti",
        basePrice: 600,
        image: "https://images.unsplash.com/photo-1564257577-4b0b5b1b1b1b?w=300",
        features: ["Daily Wear", "Comfortable", "Affordable"],
        description: "Simple and comfortable kurti for everyday wear. Made with breathable fabric for all-day comfort."
    },
    {
        name: "Wedding Blouse",
        category: "blouse",
        basePrice: 3000,
        image: "https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=300",
        features: ["Bridal", "Heavy Work", "Premium"],
        description: "Exquisite bridal blouse with heavy embroidery and premium finish. Perfect for your special day."
    },
    {
        name: "Indo-Western Dress",
        category: "dress",
        basePrice: 1500,
        image: "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=300",
        features: ["Fusion", "Modern", "Stylish"],
        description: "Perfect blend of Indian and Western styles. Ideal for modern women who love fusion fashion."
    },
    {
        name: "Festive Kurti",
        category: "kurti",
        basePrice: 1200,
        image: "https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=300",
        features: ["Festive", "Embroidered", "Premium"],
        description: "Beautiful festive kurti with intricate embroidery. Perfect for festivals and celebrations."
    },
    {
        name: "Silk Saree Blouse",
        category: "blouse",
        basePrice: 1800,
        image: "https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=300",
        features: ["Silk", "Luxury", "Traditional"],
        description: "Premium silk blouse with traditional designs. Complements any silk saree beautifully."
    },
    {
        name: "Anarkali Dress",
        category: "dress",
        basePrice: 2200,
        image: "https://images.unsplash.com/photo-1566479179817-c0b5b4b4b1e5?w=300",
        features: ["Anarkali", "Flowy", "Elegant"],
        description: "Classic Anarkali dress with flowing silhouette. Perfect for formal occasions and parties."
    },
    {
        name: "Cotton Saree",
        category: "saree",
        basePrice: 900,
        image: "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=300",
        features: ["Cotton", "Breathable", "Daily Wear"],
        description: "Comfortable cotton saree for daily wear. Easy to maintain and perfect for office or casual outings."
    },
    {
        name: "Palazzo Kurti Set",
        category: "kurti",
        basePrice: 1100,
        image: "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=300",
        features: ["Set", "Palazzo", "Trendy"],
        description: "Trendy kurti with palazzo pants set. Comfortable and stylish for modern women."
    }
];

const sampleUsers = [
    {
        name: "Priya Sharma",
        email: "<EMAIL>",
        phone: "9876543210",
        role: "customer"
    },
    {
        name: "Anjali Nair",
        email: "<EMAIL>",
        phone: "9876543211",
        role: "customer"
    },
    {
        name: "Boutique Manager",
        email: "<EMAIL>",
        phone: "9876543200",
        role: "manager"
    },
    {
        name: "Meera Krishna",
        email: "<EMAIL>",
        phone: "9876543212",
        role: "customer"
    },
    {
        name: "Lakshmi Menon",
        email: "<EMAIL>",
        phone: "9876543213",
        role: "customer"
    }
];

// Function to seed the database
async function seedDatabase() {
    try {
        console.log('🌱 Starting database seeding...');
        
        // Clear existing data
        console.log('🗑️  Clearing existing data...');
        await Dress.deleteMany({});
        await Order.deleteMany({});
        await User.deleteMany({});
        
        // Insert dresses
        console.log('👗 Inserting dresses...');
        const insertedDresses = await Dress.insertMany(sampleDresses);
        console.log(`✅ Inserted ${insertedDresses.length} dresses`);
        
        // Insert users
        console.log('👥 Inserting users...');
        const insertedUsers = await User.insertMany(sampleUsers);
        console.log(`✅ Inserted ${insertedUsers.length} users`);
        
        // Create sample orders
        console.log('📦 Creating sample orders...');
        const sampleOrders = [
            {
                orderId: 'KB' + Date.now(),
                customerName: 'Priya Sharma',
                customerEmail: '<EMAIL>',
                customerPhone: '9876543210',
                dressId: insertedDresses[0]._id,
                measurements: {
                    bust: 34,
                    waist: 28,
                    hip: 36,
                    length: 42
                },
                customizations: {
                    fabric: 'silk',
                    color: 'red',
                    specialInstructions: 'Please add extra padding'
                },
                deliveryOption: 'express',
                deliveryAddress: 'MG Road, Ernakulam, Kochi, Kerala 682035',
                totalAmount: 1700,
                status: 'confirmed',
                porterOrderId: 'POR' + Date.now(),
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
            },
            {
                orderId: 'KB' + (Date.now() + 1),
                customerName: 'Anjali Nair',
                customerEmail: '<EMAIL>',
                customerPhone: '9876543211',
                dressId: insertedDresses[1]._id,
                measurements: {
                    bust: 32,
                    waist: 26,
                    hip: 34,
                    length: 40
                },
                customizations: {
                    fabric: 'cotton',
                    color: 'blue',
                    specialInstructions: 'Make it slightly loose fit'
                },
                deliveryOption: 'standard',
                deliveryAddress: 'Kakkanad, Kochi, Kerala 682030',
                totalAmount: 1000,
                status: 'in-progress',
                porterOrderId: 'POR' + (Date.now() + 1),
                createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) // 1 day ago
            },
            {
                orderId: 'KB' + (Date.now() + 2),
                customerName: 'Meera Krishna',
                customerEmail: '<EMAIL>',
                customerPhone: '9876543212',
                dressId: insertedDresses[2]._id,
                measurements: {
                    bust: 36,
                    waist: 30,
                    hip: 38,
                    length: 44
                },
                customizations: {
                    fabric: 'georgette',
                    color: 'black',
                    specialInstructions: 'Add sequin work on neckline'
                },
                deliveryOption: 'express',
                deliveryAddress: 'Fort Kochi, Kochi, Kerala 682001',
                totalAmount: 2700,
                status: 'ready',
                porterOrderId: 'POR' + (Date.now() + 2),
                createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6 hours ago
            },
            {
                orderId: 'KB' + (Date.now() + 3),
                customerName: 'Lakshmi Menon',
                customerEmail: '<EMAIL>',
                customerPhone: '9876543213',
                dressId: insertedDresses[3]._id,
                measurements: {
                    bust: 38,
                    waist: 32,
                    hip: 40,
                    length: 46
                },
                customizations: {
                    fabric: 'silk',
                    color: 'green',
                    specialInstructions: 'Traditional Kerala style draping'
                },
                deliveryOption: 'regular',
                deliveryAddress: 'Palarivattom, Kochi, Kerala 682025',
                totalAmount: 1800,
                status: 'delivered',
                porterOrderId: 'POR' + (Date.now() + 3),
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) // 5 days ago
            },
            {
                orderId: 'KB' + (Date.now() + 4),
                customerName: 'Priya Sharma',
                customerEmail: '<EMAIL>',
                customerPhone: '9876543210',
                dressId: insertedDresses[5]._id,
                measurements: {
                    bust: 34,
                    waist: 28,
                    hip: 36,
                    length: 42
                },
                customizations: {
                    fabric: 'silk',
                    color: 'gold',
                    specialInstructions: 'Heavy embroidery work for wedding'
                },
                deliveryOption: 'express',
                deliveryAddress: 'MG Road, Ernakulam, Kochi, Kerala 682035',
                totalAmount: 3500,
                status: 'pending',
                porterOrderId: 'POR' + (Date.now() + 4),
                createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
            }
        ];
        
        const insertedOrders = await Order.insertMany(sampleOrders);
        console.log(`✅ Inserted ${insertedOrders.length} orders`);
        
        // Display summary
        console.log('\n📊 Database Seeding Summary:');
        console.log('================================');
        console.log(`👗 Dresses: ${insertedDresses.length}`);
        console.log(`👥 Users: ${insertedUsers.length}`);
        console.log(`📦 Orders: ${insertedOrders.length}`);
        console.log('================================');
        
        // Display some sample data
        console.log('\n🎯 Sample Dress Categories:');
        const categories = [...new Set(insertedDresses.map(d => d.category))];
        categories.forEach(cat => {
            const count = insertedDresses.filter(d => d.category === cat).length;
            console.log(`   ${cat}: ${count} items`);
        });
        
        console.log('\n📈 Order Status Distribution:');
        const statuses = [...new Set(insertedOrders.map(o => o.status))];
        statuses.forEach(status => {
            const count = insertedOrders.filter(o => o.status === status).length;
            console.log(`   ${status}: ${count} orders`);
        });
        
        console.log('\n💰 Revenue Summary:');
        const totalRevenue = insertedOrders.reduce((sum, order) => sum + order.totalAmount, 0);
        const deliveredRevenue = insertedOrders
            .filter(o => o.status === 'delivered')
            .reduce((sum, order) => sum + order.totalAmount, 0);
        console.log(`   Total Orders Value: ₹${totalRevenue.toLocaleString()}`);
        console.log(`   Delivered Orders Value: ₹${deliveredRevenue.toLocaleString()}`);
        
        console.log('\n🎉 Database seeding completed successfully!');
        console.log('🚀 You can now start the server and test the application.');
        
    } catch (error) {
        console.error('❌ Error seeding database:', error);
    } finally {
        // Close the connection
        await mongoose.connection.close();
        console.log('🔌 Database connection closed.');
        process.exit(0);
    }
}

// Run the seeding function
seedDatabase();