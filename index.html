<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- SEO Meta Tags -->
    <title><PERSON><PERSON>'s Couture - Premium Custom Tailoring in Edapally, Kochi | Expert Dress Making</title>
    <meta name="description" content="Experience premium custom tailoring at <PERSON><PERSON>'s Couture in Edapally, Kochi. Expert craftsmanship, precise measurements, and quality fabrics for your perfect dress. Book your consultation today.">
    <meta name="keywords" content="custom tailoring, premium dresses, Edapally Kochi, bespoke clothing, women's fashion, dress alterations, Kerala tailoring">
    <meta name="author" content="<PERSON><PERSON>'s Couture">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<PERSON><PERSON>'s Couture - Premium Custom Tailoring in Edapally, Kochi">
    <meta property="og:description" content="Experience premium custom tailoring with precise measurements, quality fabrics, and expert craftsmanship in Edapally, Kochi.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://nishiscouture.com">
    <meta property="og:image" content="https://nishiscouture.com/images/og-image.jpg">
    <meta property="og:locale" content="en_IN">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Nishi's Couture - Premium Custom Tailoring">
    <meta name="twitter:description" content="Experience premium custom tailoring with precise measurements, quality fabrics, and expert craftsmanship in Edapally, Kochi.">
    <meta name="twitter:image" content="https://nishiscouture.com/images/twitter-image.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <meta name="theme-color" content="#ed7d47">

    <!-- Preconnect for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "Nishi's Couture",
      "description": "Premium custom tailoring services in Edapally, Kochi",
      "url": "https://nishiscouture.com",
      "telephone": "+91-98765-43210",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Fashion Plaza",
        "addressLocality": "Edapally",
        "addressRegion": "Kerala",
        "postalCode": "682024",
        "addressCountry": "IN"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "10.0261",
        "longitude": "76.3105"
      },
      "openingHours": "Mo-Sa 09:00-19:00",
      "priceRange": "₹₹₹",
      "serviceArea": {
        "@type": "GeoCircle",
        "geoMidpoint": {
          "@type": "GeoCoordinates",
          "latitude": "10.0261",
          "longitude": "76.3105"
        },
        "geoRadius": "25000"
      },
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Custom Tailoring Services",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Custom Dress Making",
              "description": "Bespoke dress tailoring with precise measurements"
            }
          }
        ]
      }
    }
    </script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-cut"></i> Nishi's Couture</h1>
                <p>Premium Custom Tailoring</p>
            </div>
            <nav class="nav-menu">
                <a href="#home" class="nav-link active">Home</a>
                <a href="#dresses" class="nav-link">Dresses</a>
                <a href="#about" class="nav-link">About</a>
                <a href="#contact" class="nav-link">Contact</a>
                <button class="btn-login" onclick="openModal('loginModal')">
                    <i class="fas fa-user"></i> Login
                </button>
            </nav>
            <div class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation -->
    <div class="mobile-overlay" onclick="closeMobileMenu()"></div>
    <nav class="mobile-nav">
        <div class="mobile-nav-header">
            <div>
                <h2><i class="fas fa-cut"></i> Nishi's Couture</h2>
                <p>Premium Custom Tailoring</p>
            </div>
            <div class="mobile-nav-close" onclick="closeMobileMenu()">
                <i class="fas fa-times"></i>
            </div>
        </div>
        <div class="mobile-nav-links">
            <a href="#home" onclick="closeMobileMenu()">
                <i class="fas fa-home"></i> Home
            </a>
            <a href="#dresses" onclick="closeMobileMenu()">
                <i class="fas fa-tshirt"></i> Dresses
            </a>
            <a href="#about" onclick="closeMobileMenu()">
                <i class="fas fa-info-circle"></i> About
            </a>
            <a href="#contact" onclick="closeMobileMenu()">
                <i class="fas fa-phone"></i> Contact
            </a>
            <a href="#" onclick="openModal('loginModal'); closeMobileMenu();">
                <i class="fas fa-user"></i> Login
            </a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1>Perfect Fit, Perfect Style</h1>
            <p>Premium custom tailoring for the modern woman in Edapally, Kochi</p>
            <div class="hero-features">
                <div class="feature">
                    <i class="fas fa-clock"></i>
                    <span>Same Day Delivery</span>
                </div>
                <div class="feature">
                    <i class="fas fa-ruler"></i>
                    <span>Smart Measurements</span>
                </div>
                <div class="feature">
                    <i class="fas fa-truck"></i>
                    <span>Porter Delivery</span>
                </div>
            </div>
            <button class="cta-button" onclick="scrollToSection('dresses')">
                Shop Now <i class="fas fa-arrow-right"></i>
            </button>
        </div>
        <div class="hero-image">
            <img src="https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500" alt="Beautiful dress">
        </div>
    </section>

    <!-- Dresses Section -->
    <section id="dresses" class="dresses-section">
        <div class="container">
            <h2>Our Collection</h2>
            
            <!-- Enhanced Filter Section -->
            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label>Category</label>
                        <select class="filter-select" id="categoryFilter">
                            <option value="all">All Categories</option>
                            <option value="saree">Sarees</option>
                            <option value="kurti">Kurtis</option>
                            <option value="dress">Western Dresses</option>
                            <option value="blouse">Blouses</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>Price Range</label>
                        <div class="price-range">
                            <span>₹500</span>
                            <input type="range" class="price-slider" id="priceRange" min="500" max="5000" value="5000">
                            <span id="priceValue">₹5000</span>
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <label>Delivery</label>
                        <select class="filter-select" id="deliveryFilter">
                            <option value="all">All Options</option>
                            <option value="express">Express (Same Day)</option>
                            <option value="standard">Standard (Next Day)</option>
                            <option value="regular">Regular (2-3 Days)</option>
                        </select>
                    </div>
                </div>
                
                <div class="filter-row">
                    <div class="sort-options">
                        <label>Sort by:</label>
                        <button class="sort-btn active" data-sort="featured">Featured</button>
                        <button class="sort-btn" data-sort="price-low">Price: Low to High</button>
                        <button class="sort-btn" data-sort="price-high">Price: High to Low</button>
                        <button class="sort-btn" data-sort="rating">Highest Rated</button>
                        <button class="sort-btn" data-sort="newest">Newest</button>
                    </div>
                    
                    <button class="cta-button" onclick="clearFilters()" style="padding: 0.5rem 1rem; font-size: 0.9rem;">
                        <i class="fas fa-refresh"></i> Clear Filters
                    </button>
                </div>
            </div>
            
            <!-- Quick Filter Tabs (Mobile Friendly) -->
            <div class="filter-tabs">
                <button class="filter-btn active" data-category="all">
                    <i class="fas fa-th"></i> All
                </button>
                <button class="filter-btn" data-category="saree">
                    <i class="fas fa-female"></i> Sarees
                </button>
                <button class="filter-btn" data-category="kurti">
                    <i class="fas fa-tshirt"></i> Kurtis
                </button>
                <button class="filter-btn" data-category="dress">
                    <i class="fas fa-dress"></i> Western
                </button>
                <button class="filter-btn" data-category="blouse">
                    <i class="fas fa-cut"></i> Blouses
                </button>
            </div>
            
            <!-- Results Info -->
            <div class="results-info">
                <span id="resultsCount">Showing all dresses</span>
                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid" title="Grid View">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" data-view="list" title="List View">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
            
            <div class="dresses-grid" id="dressesGrid">
                <!-- Dresses will be loaded here -->
            </div>
            
            <!-- Load More Button -->
            <div class="load-more-section" style="text-align: center; margin-top: 3rem;">
                <button class="cta-button" id="loadMoreBtn" onclick="loadMoreDresses()">
                    <i class="fas fa-plus"></i> Load More Dresses
                </button>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2>Why Choose Nishi's Couture?</h2>
                    <div class="features-list">
                        <div class="feature-item">
                            <i class="fas fa-magic"></i>
                            <div>
                                <h3>Smart Measurement System</h3>
                                <p>Our innovative measurement tool uses your phone camera to get accurate measurements in minutes</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-bolt"></i>
                            <div>
                                <h3>Express Tailoring</h3>
                                <p>Need it today? We specialize in last-minute orders with same-day delivery in Kochi</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-heart"></i>
                            <div>
                                <h3>Perfect Fit Guarantee</h3>
                                <p>We ensure every dress fits perfectly or we'll alter it for free</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <img src="https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=400" alt="Tailoring">
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="container">
            <h2>Get In Touch</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <h3>Location</h3>
                            <p>Fashion Plaza, Edapally, Kochi, Kerala 682024</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h3>Phone</h3>
                            <p>+91 9876543210</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h3>Email</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <form>
                        <input type="text" placeholder="Your Name" required>
                        <input type="email" placeholder="Your Email" required>
                        <textarea placeholder="Your Message" rows="5" required></textarea>
                        <button type="submit" class="submit-btn">Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Nishi's Couture</h3>
                    <p>Your trusted partner for premium custom tailoring in Edapally, Kochi</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#dresses">Dresses</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="#">Same Day Delivery</a></li>
                        <li><a href="#">Custom Tailoring</a></li>
                        <li><a href="#">Alterations</a></li>
                        <li><a href="#">Express Orders</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Nishi's Couture. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Modals -->
    <!-- OTP Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('loginModal')">&times;</span>
            <div class="auth-header">
                <i class="fas fa-mobile-alt" style="font-size: 2rem; color: #ff6b9d; margin-bottom: 1rem;"></i>
                <h2>Login with OTP</h2>
                <p>Enter your phone number to receive a verification code</p>
            </div>
            
            <!-- Phone Number Step -->
            <div id="phoneStep" class="auth-step">
                <form id="phoneForm">
                    <div class="phone-input-group">
                        <select class="country-code">
                            <option value="+91">🇮🇳 +91</option>
                        </select>
                        <input type="tel" id="phoneNumber" placeholder="Enter phone number" required maxlength="10" pattern="[0-9]{10}">
                    </div>
                    <button type="submit" class="auth-btn">
                        <i class="fas fa-paper-plane"></i> Send OTP
                    </button>
                </form>
            </div>
            
            <!-- OTP Verification Step -->
            <div id="otpStep" class="auth-step" style="display: none;">
                <form id="otpForm">
                    <p class="otp-info">We've sent a 6-digit code to <span id="sentToNumber"></span></p>
                    <div class="otp-input-group">
                        <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]">
                        <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]">
                        <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]">
                        <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]">
                        <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]">
                        <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]">
                    </div>
                    <div class="otp-actions">
                        <button type="submit" class="auth-btn">
                            <i class="fas fa-check"></i> Verify OTP
                        </button>
                        <button type="button" class="resend-btn" onclick="resendOTP()" disabled>
                            <i class="fas fa-redo"></i> Resend OTP (<span id="countdown">30</span>s)
                        </button>
                    </div>
                    <button type="button" class="back-btn" onclick="backToPhone()">
                        <i class="fas fa-arrow-left"></i> Change Number
                    </button>
                </form>
            </div>
            
            <div class="auth-footer">
                <p>New to Nishi's Couture? <a href="#" onclick="switchModal('loginModal', 'registerModal')">Create Account</a></p>
            </div>
        </div>
    </div>

    <!-- OTP Registration Modal -->
    <div id="registerModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('registerModal')">&times;</span>
            <div class="auth-header">
                <i class="fas fa-user-plus" style="font-size: 2rem; color: #ff6b9d; margin-bottom: 1rem;"></i>
                <h2>Create Account</h2>
                <p>Join Nishi's Couture for personalized tailoring</p>
            </div>
            
            <!-- Registration Form -->
            <div id="registerStep" class="auth-step">
                <form id="registerForm">
                    <input type="text" id="fullName" placeholder="Full Name" required>
                    <input type="email" id="email" placeholder="Email Address" required>
                    <div class="phone-input-group">
                        <select class="country-code">
                            <option value="+91">🇮🇳 +91</option>
                        </select>
                        <input type="tel" id="regPhoneNumber" placeholder="Phone Number" required maxlength="10" pattern="[0-9]{10}">
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="agreeTerms" required>
                        <label for="agreeTerms">I agree to the <a href="#" target="_blank">Terms & Conditions</a></label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="agreeMarketing">
                        <label for="agreeMarketing">Send me updates about new collections and offers</label>
                    </div>
                    <button type="submit" class="auth-btn">
                        <i class="fas fa-user-plus"></i> Create Account
                    </button>
                </form>
            </div>
            
            <!-- OTP Verification for Registration -->
            <div id="regOtpStep" class="auth-step" style="display: none;">
                <form id="regOtpForm">
                    <p class="otp-info">Verify your phone number to complete registration</p>
                    <p class="otp-info">Code sent to <span id="regSentToNumber"></span></p>
                    <div class="otp-input-group">
                        <input type="text" class="reg-otp-digit" maxlength="1" pattern="[0-9]">
                        <input type="text" class="reg-otp-digit" maxlength="1" pattern="[0-9]">
                        <input type="text" class="reg-otp-digit" maxlength="1" pattern="[0-9]">
                        <input type="text" class="reg-otp-digit" maxlength="1" pattern="[0-9]">
                        <input type="text" class="reg-otp-digit" maxlength="1" pattern="[0-9]">
                        <input type="text" class="reg-otp-digit" maxlength="1" pattern="[0-9]">
                    </div>
                    <div class="otp-actions">
                        <button type="submit" class="auth-btn">
                            <i class="fas fa-check"></i> Complete Registration
                        </button>
                        <button type="button" class="resend-btn" onclick="resendRegOTP()" disabled>
                            <i class="fas fa-redo"></i> Resend OTP (<span id="regCountdown">30</span>s)
                        </button>
                    </div>
                    <button type="button" class="back-btn" onclick="backToRegister()">
                        <i class="fas fa-arrow-left"></i> Back to Form
                    </button>
                </form>
            </div>
            
            <div class="auth-footer">
                <p>Already have an account? <a href="#" onclick="switchModal('registerModal', 'loginModal')">Login</a></p>
            </div>
        </div>
    </div>

    <script src="enhanced-script.js"></script>
</body>
</html>