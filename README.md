# Nishi's Couture - Premium Custom Tailoring

A mobile-friendly ecommerce website for premium custom tailoring services in Edapally, Kochi, specializing in personalized tailoring for ladies with innovative measurement collection and Porter delivery integration.

## Features

### Customer Features
- **Mobile-First Design**: Responsive design optimized for mobile devices
- **Smart Measurement System**: Innovative phone camera-based measurement collection
- **Last-Minute Tailoring**: Express delivery options for urgent orders
- **Custom Dress Ordering**: Personalize dresses with fabric, color, and size options
- **Porter Integration**: Seamless delivery through Porter app
- **Dynamic Pricing**: Price based on urgency and quality requirements
- **Attractive UI**: Eye-catching design specifically for ladies

### Manager Features
- **Dress Management**: Upload, edit, and manage dress catalog
- **Order Management**: Track and update order status
- **Analytics Dashboard**: View sales, orders, and performance metrics
- **Real-time Updates**: Live order status updates

### Technical Features
- **Express.js Backend**: RESTful API with MongoDB
- **File Upload**: Multer integration for dress images
- **Responsive Design**: Mobile-first CSS with modern styling
- **Real-time Updates**: Dynamic content loading
- **Porter API Integration**: Delivery service integration

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd nishis-couture
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up MongoDB**
   - Install MongoDB locally or use MongoDB Atlas
   - Update the MONGODB_URI in .env file

4. **Configure environment variables**
   - Copy .env.example to .env
   - Update the values as needed

5. **Create uploads directory**
   ```bash
   mkdir uploads
   ```

6. **Start the server**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

7. **Access the application**
   - Customer Website: http://localhost:3000
   - Manager Dashboard: http://localhost:3000/manager

## Project Structure

```
nishis-couture/
├── index.html          # Main customer website
├── manager.html        # Manager dashboard
├── styles.css          # CSS styles
├── script.js           # Frontend JavaScript
├── server.js           # Express.js server
├── package.json        # Dependencies
├── .env               # Environment variables
├── uploads/           # Uploaded dress images
└── README.md          # This file
```

## API Endpoints

### Dresses
- `GET /api/dresses` - Get all active dresses
- `POST /api/dresses` - Add new dress (with image upload)
- `PUT /api/dresses/:id` - Update dress
- `DELETE /api/dresses/:id` - Delete dress

### Orders
- `POST /api/orders` - Place new order
- `GET /api/orders` - Get all orders (manager)
- `PUT /api/orders/:id/status` - Update order status

### Analytics
- `GET /api/analytics` - Get dashboard analytics

## Key Features Explained

### Smart Measurement System
The innovative measurement system uses the phone's camera to capture accurate measurements:
- User stands in good lighting
- AI-powered measurement calculation
- Automatic size recommendations
- Manual entry fallback option

### Porter Integration
Seamless integration with Porter delivery service:
- Real-time delivery cost calculation
- Order tracking through Porter app
- Express delivery options (same-day, next-day)
- Automatic dispatch notifications

### Dynamic Pricing
Intelligent pricing based on:
- **Urgency**: Express (+₹500), Standard (+₹200), Regular (Free)
- **Fabric**: Silk (+₹300), Georgette (+₹200), Chiffon (+₹250), Cotton (Free)
- **Customizations**: Additional charges for special requests

### Mobile-First Design
- Touch-friendly interface
- Optimized for small screens
- Fast loading times
- Intuitive navigation

## Customization

### Adding New Dress Categories
1. Update the category options in both frontend and backend
2. Add new filter buttons in the main website
3. Update the manager dashboard category dropdown

### Modifying Pricing Logic
Update the pricing calculations in:
- `script.js` - Frontend price display
- `server.js` - Backend order processing

### Styling Changes
All styles are in `styles.css` with CSS custom properties for easy theming:
- Primary color: `#ff6b9d` (Pink)
- Secondary color: `#c44569` (Dark Pink)
- Background: `#f8f9fa` (Light Gray)

## Production Deployment

1. **Environment Setup**
   - Set NODE_ENV=production
   - Configure production MongoDB URI
   - Set up proper Porter API credentials

2. **Security**
   - Implement proper authentication
   - Add rate limiting
   - Set up HTTPS
   - Validate all inputs

3. **Performance**
   - Enable gzip compression
   - Optimize images
   - Implement caching
   - Use CDN for static assets

## Porter Integration Setup

1. **Get Porter API Credentials**
   - Sign up at https://porter.in/
   - Get API key from dashboard
   - Update PORTER_API_KEY in .env

2. **Configure Pickup Location**
   - Update pickup coordinates in server.js
   - Set business address and contact details

3. **Test Integration**
   - Use Porter's sandbox environment first
   - Test order placement and tracking
   - Verify webhook endpoints

## Support

For support and customization requests, contact:
- Email: <EMAIL>
- Phone: +91 **********
- Address: MG Road, Kochi, Kerala 682035

## License

This project is licensed under the MIT License.